import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  GetFunctionCodeSigningConfigRequest,
  GetFunctionCodeSigningConfigResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetFunctionCodeSigningConfigCommandInput
  extends GetFunctionCodeSigningConfigRequest {}
export interface GetFunctionCodeSigningConfigCommandOutput
  extends GetFunctionCodeSigningConfigResponse,
    __MetadataBearer {}
declare const GetFunctionCodeSigningConfigCommand_base: {
  new (
    input: GetFunctionCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetFunctionCodeSigningConfigCommandInput,
    GetFunctionCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetFunctionCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetFunctionCodeSigningConfigCommandInput,
    GetFunctionCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetFunctionCodeSigningConfigCommand extends GetFunctionCodeSigningConfigCommand_base {
  protected static __types: {
    api: {
      input: GetFunctionCodeSigningConfigRequest;
      output: GetFunctionCodeSigningConfigResponse;
    };
    sdk: {
      input: GetFunctionCodeSigningConfigCommandInput;
      output: GetFunctionCodeSigningConfigCommandOutput;
    };
  };
}
