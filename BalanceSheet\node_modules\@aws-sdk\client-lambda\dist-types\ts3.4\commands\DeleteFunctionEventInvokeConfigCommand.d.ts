import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { DeleteFunctionEventInvokeConfigRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteFunctionEventInvokeConfigCommandInput
  extends DeleteFunctionEventInvokeConfigRequest {}
export interface DeleteFunctionEventInvokeConfigCommandOutput
  extends __MetadataBearer {}
declare const DeleteFunctionEventInvokeConfigCommand_base: {
  new (
    input: DeleteFunctionEventInvokeConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFunctionEventInvokeConfigCommandInput,
    DeleteFunctionEventInvokeConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteFunctionEventInvokeConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFunctionEventInvokeConfigCommandInput,
    DeleteFunctionEventInvokeConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteFunctionEventInvokeConfigCommand extends DeleteFunctionEventInvokeConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteFunctionEventInvokeConfigRequest;
      output: {};
    };
    sdk: {
      input: DeleteFunctionEventInvokeConfigCommandInput;
      output: DeleteFunctionEventInvokeConfigCommandOutput;
    };
  };
}
