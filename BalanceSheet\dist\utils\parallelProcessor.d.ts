import { XeroRequestData, ProcessedRowData } from '../types';
export interface ParallelProcessingOptions {
    concurrency?: number;
    batchSize?: number;
    retryAttempts?: number;
    retryDelay?: number;
    timeout?: number;
}
export interface ProcessingResult<T> {
    success: boolean;
    data?: T;
    error?: Error;
    duration: number;
    month: string;
    attempt: number;
}
export interface BatchResult<T> {
    results: ProcessingResult<T>[];
    totalDuration: number;
    successCount: number;
    errorCount: number;
    errors: Array<{
        month: string;
        error: Error;
    }>;
}
export declare function generateMonthRanges(startDate: string, endDate: string): Array<{
    startDate: string;
    endDate: string;
    month: string;
}>;
export declare function processMonthsInParallel<T>(processor: (requestData: XeroRequestData) => Promise<T>, baseRequestData: XeroRequestData, options?: ParallelProcessingOptions): Promise<BatchResult<T>>;
export declare function combineMonthResults(results: ProcessingResult<ProcessedRowData[]>[]): ProcessedRowData[];
export declare function processBatchedMonths<T>(processor: (requestData: XeroRequestData) => Promise<T>, baseRequestData: XeroRequestData, options?: ParallelProcessingOptions): Promise<BatchResult<T>>;
//# sourceMappingURL=parallelProcessor.d.ts.map