import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  ListFunctionUrlConfigsRequest,
  ListFunctionUrlConfigsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListFunctionUrlConfigsCommandInput
  extends ListFunctionUrlConfigsRequest {}
export interface ListFunctionUrlConfigsCommandOutput
  extends ListFunctionUrlConfigsResponse,
    __MetadataBearer {}
declare const ListFunctionUrlConfigsCommand_base: {
  new (
    input: ListFunctionUrlConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFunctionUrlConfigsCommandInput,
    ListFunctionUrlConfigsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListFunctionUrlConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFunctionUrlConfigsCommandInput,
    ListFunctionUrlConfigsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListFunctionUrlConfigsCommand extends ListFunctionUrlConfigsCommand_base {
  protected static __types: {
    api: {
      input: ListFunctionUrlConfigsRequest;
      output: ListFunctionUrlConfigsResponse;
    };
    sdk: {
      input: ListFunctionUrlConfigsCommandInput;
      output: ListFunctionUrlConfigsCommandOutput;
    };
  };
}
