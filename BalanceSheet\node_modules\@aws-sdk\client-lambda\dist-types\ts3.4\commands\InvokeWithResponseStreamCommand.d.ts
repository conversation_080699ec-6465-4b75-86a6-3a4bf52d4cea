import { Command as $Command } from "@smithy/smithy-client";
import {
  BlobPayloadInputTypes,
  MetadataBearer as __MetadataBearer,
} from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  InvokeWithResponseStreamRequest,
  InvokeWithResponseStreamResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export type InvokeWithResponseStreamCommandInputType = Pick<
  InvokeWithResponseStreamRequest,
  Exclude<keyof InvokeWithResponseStreamRequest, "Payload">
> & {
  Payload?: BlobPayloadInputTypes;
};
export interface InvokeWithResponseStreamCommandInput
  extends InvokeWithResponseStreamCommandInputType {}
export interface InvokeWithResponseStreamCommandOutput
  extends InvokeWithResponseStreamResponse,
    __MetadataBearer {}
declare const InvokeWithResponseStreamCommand_base: {
  new (
    input: InvokeWithResponseStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    InvokeWithResponseStreamCommandInput,
    InvokeWithResponseStreamCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: InvokeWithResponseStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    InvokeWithResponseStreamCommandInput,
    InvokeWithResponseStreamCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class InvokeWithResponseStreamCommand extends InvokeWithResponseStreamCommand_base {
  protected static __types: {
    api: {
      input: InvokeWithResponseStreamRequest;
      output: InvokeWithResponseStreamResponse;
    };
    sdk: {
      input: InvokeWithResponseStreamCommandInput;
      output: InvokeWithResponseStreamCommandOutput;
    };
  };
}
