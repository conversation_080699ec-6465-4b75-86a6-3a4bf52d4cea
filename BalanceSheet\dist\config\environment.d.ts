import { EnvironmentConfig } from '../types';
export declare function getEnvironmentConfig(): EnvironmentConfig;
export declare function isOfflineMode(): boolean;
export declare function getXeroConfig(): {
    clientId: string;
    clientSecret: string;
    tokenUrl: string;
    baseUrl: string;
};
export declare function getAWSConfig(): {
    region: string;
    accessKeyId: string | undefined;
    secretAccessKey: string | undefined;
};
//# sourceMappingURL=environment.d.ts.map