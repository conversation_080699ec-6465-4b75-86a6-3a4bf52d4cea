import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketAccelerateConfigurationOutput,
  GetBucketAccelerateConfigurationRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketAccelerateConfigurationCommandInput
  extends GetBucketAccelerateConfigurationRequest {}
export interface GetBucketAccelerateConfigurationCommandOutput
  extends GetBucketAccelerateConfigurationOutput,
    __MetadataBearer {}
declare const GetBucketAccelerateConfigurationCommand_base: {
  new (
    input: GetBucketAccelerateConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketAccelerateConfigurationCommandInput,
    GetBucketAccelerateConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketAccelerateConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketAccelerateConfigurationCommandInput,
    GetBucketAccelerateConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketAccelerateConfigurationCommand extends GetBucketAccelerateConfigurationCommand_base {
  protected static __types: {
    api: {
      input: GetBucketAccelerateConfigurationRequest;
      output: GetBucketAccelerateConfigurationOutput;
    };
    sdk: {
      input: GetBucketAccelerateConfigurationCommandInput;
      output: GetBucketAccelerateConfigurationCommandOutput;
    };
  };
}
