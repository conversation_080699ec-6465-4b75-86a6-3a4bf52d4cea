/**
 * Integration Logger Utility for Balance Sheet Service
 *
 * This utility provides comprehensive logging functionality for Balance Sheet
 * synchronization operations using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - API call tracking with detailed metrics
 * - Sync operation monitoring with status management
 * - Error handling and diagnostic information
 * - Performance metrics and timing data
 *
 * <AUTHOR> Sheet Integration Logger
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';

/**
 * Sync Status Enum (matching Prisma schema)
 */
export enum SyncStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    SUCCESS = 'SUCCESS',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    RETRYING = 'RETRYING',
    CANCELLED = 'CANCELLED'
}

/**
 * Integration Log Data Interface
 */
export interface IntegrationLogData {
    requestId?: string;
    companyId: string;
    apiName: string;
    method?: string;
    apiUrl?: string;
    integrationName?: string;
    statusCode?: string;
    duration?: string;
    message?: string;
    entity?: string;
    triggeredBy?: 'USER' | 'SYSTEM';
    syncStatus?: SyncStatus;
    apiRequest?: any;
    apiResponse?: any;
    errorDetails?: any;
    syncSummary?: any;
    startedAt?: Date;
    completedAt?: Date;
}

/**
 * Lambda Execution Context
 */
export interface LambdaExecutionContext {
    requestId: string;
    companyId: string;
    functionName: string;
    startTime: number;
    entity: string;
    triggeredBy: 'USER' | 'SYSTEM';
}

/**
 * Sync Summary Interface
 */
export interface SyncSummary {
    totalMonths: number;
    processedMonths: number;
    trackingRecords: number;
    summaryRecords: number;
    apiCalls: number;
    errors: number;
    warnings: number;
    duration: string;
    s3FilesCreated?: number;
}

/**
 * Get Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    return new PrismaClient();
}

/**
 * Create a new integration log entry
 */
export async function createIntegrationLog(logData: IntegrationLogData): Promise<string> {
    const prisma = getPrismaClient();
    
    try {
        const log = await prisma.integrationLog.create({
            data: {
                Id: uuidv4(),
                RequestId: logData.requestId || uuidv4(),
                CompanyId: logData.companyId,
                ApiName: logData.apiName,
                Method: logData.method,
                ApiUrl: logData.apiUrl,
                IntegrationName: logData.integrationName || 'Xero',
                StatusCode: logData.statusCode,
                Duration: logData.duration,
                Message: logData.message,
                Entity: logData.entity,
                TriggeredBy: logData.triggeredBy,
                SyncStatus: logData.syncStatus || SyncStatus.PENDING,
                ApiRequest: logData.apiRequest,
                ApiResponse: logData.apiResponse,
                ErrorDetails: logData.errorDetails,
                SyncSummary: logData.syncSummary,
                StartedAt: logData.startedAt || new Date(),
                CompletedAt: logData.completedAt,
            },
        });
        
        console.log(`📝 Created integration log: ${log.Id} (RequestId: ${log.RequestId})`);
        return log.Id;
    } catch (error) {
        console.error('❌ Failed to create integration log:', error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

/**
 * Update an existing integration log
 */
export async function updateIntegrationLog(
    logId: string,
    updateData: Partial<IntegrationLogData>
): Promise<void> {
    const prisma = getPrismaClient();
    
    try {
        await prisma.integrationLog.update({
            where: { Id: logId },
            data: {
                ...(updateData.statusCode && { StatusCode: updateData.statusCode }),
                ...(updateData.duration && { Duration: updateData.duration }),
                ...(updateData.message && { Message: updateData.message }),
                ...(updateData.syncStatus && { SyncStatus: updateData.syncStatus }),
                ...(updateData.apiResponse && { ApiResponse: updateData.apiResponse }),
                ...(updateData.errorDetails && { ErrorDetails: updateData.errorDetails }),
                ...(updateData.syncSummary && { SyncSummary: updateData.syncSummary }),
                ...(updateData.completedAt && { CompletedAt: updateData.completedAt }),
                UpdatedAt: new Date(),
            },
        });
        
        console.log(`📝 Updated integration log: ${logId}`);
    } catch (error) {
        console.error(`❌ Failed to update integration log ${logId}:`, error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

/**
 * Log Lambda function start
 */
export async function logLambdaStart(context: LambdaExecutionContext): Promise<string> {
    const logData: IntegrationLogData = {
        requestId: context.requestId,
        companyId: context.companyId,
        apiName: 'BalanceSheetSync',
        method: 'POST',
        apiUrl: `/xero/sync-balance-sheet`,
        integrationName: 'Xero',
        message: `Balance Sheet sync started for ${context.entity}`,
        entity: context.entity,
        triggeredBy: context.triggeredBy,
        syncStatus: SyncStatus.IN_PROGRESS,
        startedAt: new Date(context.startTime),
    };
    
    return await createIntegrationLog(logData);
}

/**
 * Log Lambda function success
 */
export async function logLambdaSuccess(
    logId: string,
    context: LambdaExecutionContext,
    syncSummary: SyncSummary
): Promise<void> {
    const duration = Date.now() - context.startTime;
    const durationFormatted = formatDuration(duration);
    
    const updateData: Partial<IntegrationLogData> = {
        statusCode: '200',
        duration: durationFormatted,
        message: `Balance Sheet sync completed successfully. Processed ${syncSummary.processedMonths} months with ${syncSummary.trackingRecords + syncSummary.summaryRecords} total records.`,
        syncStatus: syncSummary.warnings > 0 ? SyncStatus.WARNING : SyncStatus.SUCCESS,
        syncSummary: syncSummary,
        completedAt: new Date(),
    };
    
    await updateIntegrationLog(logId, updateData);
    console.log(`✅ Lambda execution completed successfully in ${durationFormatted}`);
}

/**
 * Log Lambda function failure
 */
export async function logLambdaFailure(
    logId: string,
    context: LambdaExecutionContext,
    error: Error,
    partialSummary?: Partial<SyncSummary>
): Promise<void> {
    const duration = Date.now() - context.startTime;
    const durationFormatted = formatDuration(duration);
    
    const errorDetails = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        functionName: context.functionName,
        requestId: context.requestId,
    };
    
    const updateData: Partial<IntegrationLogData> = {
        statusCode: '500',
        duration: durationFormatted,
        message: `Balance Sheet sync failed: ${error.message}`,
        syncStatus: SyncStatus.ERROR,
        errorDetails: errorDetails,
        syncSummary: partialSummary,
        completedAt: new Date(),
    };
    
    await updateIntegrationLog(logId, updateData);
    console.error(`❌ Lambda execution failed after ${durationFormatted}:`, error.message);
}

/**
 * Create Lambda execution context
 */
export function createLambdaContext(
    companyId: string,
    entity: string = 'BalanceSheet',
    triggeredBy: 'USER' | 'SYSTEM' = 'SYSTEM'
): LambdaExecutionContext {
    return {
        requestId: uuidv4(),
        companyId,
        functionName: process.env.AWS_LAMBDA_FUNCTION_NAME || 'balance-sheet-sync',
        startTime: Date.now(),
        entity,
        triggeredBy,
    };
}

/**
 * Format duration in human-readable format
 */
function formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) {
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    } else {
        return `${seconds}s`;
    }
}

/**
 * Create sync summary object
 */
export function createSyncSummary(
    totalMonths: number,
    processedMonths: number,
    trackingRecords: number,
    summaryRecords: number,
    apiCalls: number,
    errors: number = 0,
    warnings: number = 0,
    duration: string,
    s3FilesCreated?: number
): SyncSummary {
    return {
        totalMonths,
        processedMonths,
        trackingRecords,
        summaryRecords,
        apiCalls,
        errors,
        warnings,
        duration,
        s3FilesCreated,
    };
}

/**
 * Log API call details
 */
export async function logApiCall(
    requestId: string,
    companyId: string,
    apiName: string,
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    request?: any,
    response?: any,
    error?: any
): Promise<string> {
    const logData: IntegrationLogData = {
        requestId,
        companyId,
        apiName,
        method,
        apiUrl: url,
        integrationName: 'Xero',
        statusCode: statusCode.toString(),
        duration: `${duration}ms`,
        message: error ? `API call failed: ${error.message}` : `API call completed successfully`,
        entity: 'API',
        triggeredBy: 'SYSTEM',
        syncStatus: error ? SyncStatus.ERROR : SyncStatus.SUCCESS,
        apiRequest: request,
        apiResponse: response,
        errorDetails: error,
        startedAt: new Date(),
        completedAt: new Date(),
    };
    
    return await createIntegrationLog(logData);
}
