"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getXeroTrackingCategories = getXeroTrackingCategories;
const axiosInstance_1 = __importDefault(require("../utils/axiosInstance"));
const environment_1 = require("../config/environment");
async function getXeroTrackingCategories(accessToken, tenantId) {
    const { baseUrl } = (0, environment_1.getXeroConfig)();
    const url = `${baseUrl}TrackingCategories`;
    try {
        const response = await axiosInstance_1.default.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
            },
        });
        if (!response.data?.TrackingCategories) {
            console.warn('No tracking categories found in Xero response');
            return [];
        }
        return response.data.TrackingCategories.map((category) => ({
            categoryId: category.TrackingCategoryID,
            name: category.Name,
            status: category.Status,
            options: category.Options?.map((option) => ({
                trackingOptionId: option.TrackingOptionID,
                name: option.Name,
                status: option.Status,
            })) || []
        }));
    }
    catch (error) {
        console.error('Failed to fetch Xero tracking categories:', error);
        if (error.response?.status === 429) {
            const retryAfter = error.response.headers['retry-after'] || 60;
            throw new Error(`Xero API rate limit exceeded while fetching tracking categories. Retry after ${retryAfter} seconds.`);
        }
        console.warn('Continuing without tracking categories due to API error');
        return [];
    }
}
//# sourceMappingURL=getXeroTrackingCategories.js.map