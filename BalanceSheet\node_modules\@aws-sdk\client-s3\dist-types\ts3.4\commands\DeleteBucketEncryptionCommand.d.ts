import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketEncryptionRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketEncryptionCommandInput
  extends DeleteBucketEncryptionRequest {}
export interface DeleteBucketEncryptionCommandOutput extends __MetadataBearer {}
declare const DeleteBucketEncryptionCommand_base: {
  new (
    input: DeleteBucketEncryptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketEncryptionCommandInput,
    DeleteBucketEncryptionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteBucketEncryptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketEncryptionCommandInput,
    DeleteBucketEncryptionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketEncryptionCommand extends DeleteBucketEncryptionCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketEncryptionRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketEncryptionCommandInput;
      output: DeleteBucketEncryptionCommandOutput;
    };
  };
}
