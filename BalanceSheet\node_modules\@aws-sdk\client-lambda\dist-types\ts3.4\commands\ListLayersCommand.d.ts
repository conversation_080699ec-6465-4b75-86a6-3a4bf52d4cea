import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { ListLayersRequest, ListLayersResponse } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListLayersCommandInput extends ListLayersRequest {}
export interface ListLayersCommandOutput
  extends ListLayersResponse,
    __MetadataBearer {}
declare const ListLayersCommand_base: {
  new (
    input: ListLayersCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListLayersCommandInput,
    ListLayersCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListLayersCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListLayersCommandInput,
    ListLayersCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListLayersCommand extends ListLayersCommand_base {
  protected static __types: {
    api: {
      input: ListLayersRequest;
      output: ListLayersResponse;
    };
    sdk: {
      input: ListLayersCommandInput;
      output: ListLayersCommandOutput;
    };
  };
}
