"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const types_1 = require("../types");
const balanceSheetService_1 = require("../services/balanceSheetService");
const requestParser_1 = require("../utils/requestParser");
const handler = async (event, context) => {
    const timeoutWarning = setTimeout(() => {
        console.warn('⚠️ Lambda approaching timeout, consider increasing timeout or reducing batch size');
    }, (context.getRemainingTimeInMillis() * 0.8));
    try {
        if ('Records' in event && event.Records?.length) {
            console.log(`📥 Processing ${event.Records.length} SQS messages`);
            for (const record of event.Records) {
                try {
                    const requestData = JSON.parse(record.body);
                    console.log(`🔄 Processing Balance Sheet sync for company: ${requestData.companyId}`);
                    await (0, balanceSheetService_1.processBalanceSheetRequest)(requestData, context);
                    console.log(`✅ Successfully processed Balance Sheet sync for company: ${requestData.companyId}`);
                }
                catch (error) {
                    console.error(`❌ Failed to process SQS message for company: ${JSON.parse(record.body).companyId}`, {
                        error: error.message,
                        stack: error.stack,
                        messageId: record.messageId
                    });
                    throw error;
                }
            }
            console.log(`🎉 Successfully processed all ${event.Records.length} SQS messages`);
            return;
        }
        else {
            console.log('🌐 Processing API Gateway request');
            try {
                const requestData = (0, requestParser_1.parseRequestData)(event);
                console.log(`🔄 Starting Balance Sheet sync for company: ${requestData.companyId}`);
                const startTime = Date.now();
                await (0, balanceSheetService_1.processBalanceSheetRequest)(requestData, context);
                const duration = Date.now() - startTime;
                console.log(`✅ Balance Sheet sync completed in ${duration}ms for company: ${requestData.companyId}`);
                return {
                    statusCode: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Processing-Time': duration.toString()
                    },
                    body: JSON.stringify({
                        success: true,
                        message: '✅ Balance Sheet data synchronized successfully',
                        timestamp: new Date().toISOString(),
                        processingTimeMs: duration,
                        companyId: requestData.companyId
                    }),
                };
            }
            catch (error) {
                console.error('❌ Balance Sheet sync failed:', {
                    error: error.message,
                    stack: error.stack,
                    type: error.constructor.name
                });
                const statusCode = error instanceof types_1.ValidationError ? 400 : 500;
                return {
                    statusCode,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        success: false,
                        error: error.message,
                        type: error.constructor.name,
                        timestamp: new Date().toISOString(),
                        ...(statusCode === 400 && {
                            details: 'Please check your request parameters and try again'
                        })
                    }),
                };
            }
        }
    }
    finally {
        clearTimeout(timeoutWarning);
    }
};
exports.handler = handler;
//# sourceMappingURL=xeroBalanceSheetHandler.js.map