import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { LambdaClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../LambdaClient";
import { AddLayerVersionPermissionRequest, AddLayerVersionPermissionResponse } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link AddLayerVersionPermissionCommand}.
 */
export interface AddLayerVersionPermissionCommandInput extends AddLayerVersionPermissionRequest {
}
/**
 * @public
 *
 * The output of {@link AddLayerVersionPermissionCommand}.
 */
export interface AddLayerVersionPermissionCommandOutput extends AddLayerVersionPermissionResponse, __MetadataBearer {
}
declare const AddLayerVersionPermissionCommand_base: {
    new (input: AddLayerVersionPermissionCommandInput): import("@smithy/smithy-client").CommandImpl<AddLayerVersionPermissionCommandInput, AddLayerVersionPermissionCommandOutput, LambdaClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: AddLayerVersionPermissionCommandInput): import("@smithy/smithy-client").CommandImpl<AddLayerVersionPermissionCommandInput, AddLayerVersionPermissionCommandOutput, LambdaClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Adds permissions to the resource-based policy of a version of an <a href="https://docs.aws.amazon.com/lambda/latest/dg/configuration-layers.html">Lambda
 *         layer</a>. Use this action to grant layer
 *       usage permission to other accounts. You can grant permission to a single account, all accounts in an organization,
 *       or all Amazon Web Services accounts.  </p>
 *          <p>To revoke permission, call <a>RemoveLayerVersionPermission</a> with the statement ID that you
 *       specified when you added it.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { LambdaClient, AddLayerVersionPermissionCommand } from "@aws-sdk/client-lambda"; // ES Modules import
 * // const { LambdaClient, AddLayerVersionPermissionCommand } = require("@aws-sdk/client-lambda"); // CommonJS import
 * const client = new LambdaClient(config);
 * const input = { // AddLayerVersionPermissionRequest
 *   LayerName: "STRING_VALUE", // required
 *   VersionNumber: Number("long"), // required
 *   StatementId: "STRING_VALUE", // required
 *   Action: "STRING_VALUE", // required
 *   Principal: "STRING_VALUE", // required
 *   OrganizationId: "STRING_VALUE",
 *   RevisionId: "STRING_VALUE",
 * };
 * const command = new AddLayerVersionPermissionCommand(input);
 * const response = await client.send(command);
 * // { // AddLayerVersionPermissionResponse
 * //   Statement: "STRING_VALUE",
 * //   RevisionId: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param AddLayerVersionPermissionCommandInput - {@link AddLayerVersionPermissionCommandInput}
 * @returns {@link AddLayerVersionPermissionCommandOutput}
 * @see {@link AddLayerVersionPermissionCommandInput} for command's `input` shape.
 * @see {@link AddLayerVersionPermissionCommandOutput} for command's `response` shape.
 * @see {@link LambdaClientResolvedConfig | config} for LambdaClient's `config` shape.
 *
 * @throws {@link InvalidParameterValueException} (client fault)
 *  <p>One of the parameters in the request is not valid.</p>
 *
 * @throws {@link PolicyLengthExceededException} (client fault)
 *  <p>The permissions policy for the resource is too large. For more information, see <a href="https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html">Lambda quotas</a>.</p>
 *
 * @throws {@link PreconditionFailedException} (client fault)
 *  <p>The RevisionId provided does not match the latest RevisionId for the Lambda function or alias.</p>
 *          <ul>
 *             <li>
 *                <p>
 *                   <b>For AddPermission and RemovePermission API operations:</b> Call <code>GetPolicy</code> to retrieve the latest RevisionId for your resource.</p>
 *             </li>
 *             <li>
 *                <p>
 *                   <b>For all other API operations:</b> Call <code>GetFunction</code> or <code>GetAlias</code> to retrieve the latest RevisionId for your resource.</p>
 *             </li>
 *          </ul>
 *
 * @throws {@link ResourceConflictException} (client fault)
 *  <p>The resource already exists, or another operation is in progress.</p>
 *
 * @throws {@link ResourceNotFoundException} (client fault)
 *  <p>The resource specified in the request does not exist.</p>
 *
 * @throws {@link ServiceException} (server fault)
 *  <p>The Lambda service encountered an internal error.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request throughput limit was exceeded. For more information, see <a href="https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html#api-requests">Lambda quotas</a>.</p>
 *
 * @throws {@link LambdaServiceException}
 * <p>Base exception class for all service exceptions from Lambda service.</p>
 *
 *
 * @example To add permissions to a layer version
 * ```javascript
 * // The following example grants permission for the account ************ to use version 1 of a layer named my-layer.
 * const input = {
 *   Action: "lambda:GetLayerVersion",
 *   LayerName: "my-layer",
 *   Principal: "************",
 *   StatementId: "xaccount",
 *   VersionNumber: 1
 * };
 * const command = new AddLayerVersionPermissionCommand(input);
 * const response = await client.send(command);
 * /* response is
 * {
 *   RevisionId: "35d87451-f796-4a3f-a618-95a3671b0a0c",
 *   Statement: `{"Sid":"xaccount","Effect":"Allow","Principal":{"AWS":"arn:aws:iam::************:root"},"Action":"lambda:GetLayerVersion","Resource":"arn:aws:lambda:us-east-2:************:layer:my-layer:1"}`
 * }
 * *\/
 * ```
 *
 * @public
 */
export declare class AddLayerVersionPermissionCommand extends AddLayerVersionPermissionCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: AddLayerVersionPermissionRequest;
            output: AddLayerVersionPermissionResponse;
        };
        sdk: {
            input: AddLayerVersionPermissionCommandInput;
            output: AddLayerVersionPermissionCommandOutput;
        };
    };
}
