# @smithy/hash-stream-node

[![NPM version](https://img.shields.io/npm/v/@smithy/hash-stream-node/latest.svg)](https://www.npmjs.com/package/@smithy/hash-stream-node)
[![NPM downloads](https://img.shields.io/npm/dm/@smithy/hash-stream-node.svg)](https://www.npmjs.com/package/@smithy/hash-stream-node)

A utility for calculating the hash of Node.JS readable streams.

> An internal package

## Usage

You probably shouldn't, at least directly.
