"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const axiosInstance = axios_1.default.create({
    timeout: 10000,
    headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
    },
});
axiosInstance.interceptors.response.use((response) => response, (error) => {
    const status = error?.response?.status;
    const message = error?.response?.data?.message || error.message;
    console.error(`Axios error [${status || "UNKNOWN"}]: ${message}`);
    return Promise.reject(error);
});
exports.default = axiosInstance;
//# sourceMappingURL=axiosInstance.js.map