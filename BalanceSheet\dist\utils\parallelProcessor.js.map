{"version": 3, "file": "parallelProcessor.js", "sourceRoot": "", "sources": ["../../src/utils/parallelProcessor.ts"], "names": [], "mappings": ";;;;;AAsDA,kDA2CC;AAQD,0DAqFC;AAwGD,kDAqBC;AAQD,oDAsEC;AAnXD,sDAA6B;AAC7B,oDAA4B;AA+B5B,SAAgB,mBAAmB,CAC/B,SAAiB,EACjB,OAAe;IAEf,MAAM,KAAK,GAAG,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC;IAChC,MAAM,GAAG,GAAG,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC;IAC5B,MAAM,MAAM,GAAiE,EAAE,CAAC;IAEhF,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE7C,OAAO,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAGhD,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,UAAU,CAAC,GAAG,CAAC;gBACX,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;gBAClB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;gBACtB,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE;aACzB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,QAAQ,CAAC,GAAG,CAAC;gBACT,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE;gBAChB,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE;gBAChB,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE;gBACpB,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE;aACvB,CAAC,CAAC;QACP,CAAC;QAED,MAAM,CAAC,IAAI,CAAC;YACR,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC;YAC1C,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;YACtC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;SACnC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAQM,KAAK,UAAU,uBAAuB,CACzC,SAAuD,EACvD,eAAgC,EAChC,UAAqC,EAAE;IAEvC,MAAM,EACF,WAAW,GAAG,CAAC,EACf,aAAa,GAAG,CAAC,EACjB,UAAU,GAAG,IAAI,EACjB,OAAO,GAAG,KAAK,EAClB,GAAG,OAAO,CAAC;IAGZ,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;IAClF,CAAC;IAED,MAAM,WAAW,GAAQ,mBAAmB,CACxC,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,OAAO,CAC1B,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,sCAAsC,WAAW,CAAC,MAAM,6BAA6B,WAAW,EAAE,CAAC,CAAC;IAChH,OAAO,CAAC,GAAG,CAAC,6BAA6B,aAAa,aAAa,OAAO,aAAa,UAAU,IAAI,CAAC,CAAC;IAIvG,MAAM,KAAK,GAAG,IAAA,iBAAM,EAAC,WAAW,CAAC,CAAC;IAClC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,MAAM,OAAO,GAA0B,EAAE,CAAC;IAG1C,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CACzC,KAAK,CAAC,KAAK,IAAI,EAAE;QACb,MAAM,gBAAgB,GAAoB;YACtC,GAAG,eAAe;YAClB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;SACzB,CAAC;QAEF,OAAO,gBAAgB,CACnB,SAAS,EACT,gBAAgB,EAChB,KAAK,CAAC,KAAK,EACX,aAAa,EACb,UAAU,EACV,OAAO,CACV,CAAC;IACN,CAAC,CAAC,CACL,CAAC;IAGF,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAGpD,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QAClC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,IAAI,CAAC;gBACT,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,MAAM,CAAC,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACxF,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK;gBAC/B,OAAO,EAAE,CAAC;aACb,CAAC,CAAC;QACP,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;IAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAC3D,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAC1D,MAAM,MAAM,GAAG,OAAO;SACjB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;SACvB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,KAAM,EAAE,CAAC,CAAC,CAAC;IAIrD,OAAO;QACH,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;QACV,MAAM;KACT,CAAC;AACN,CAAC;AAQD,KAAK,UAAU,gBAAgB,CAC3B,SAAuD,EACvD,WAA4B,EAC5B,KAAa,EACb,WAAmB,EACnB,UAAkB,EAClB,OAAe;IAEf,IAAI,SAAS,GAAiB,IAAI,CAAC;IACnC,MAAM,QAAQ,GAAG,GAAG,WAAW,CAAC,SAAS,IAAI,KAAK,EAAE,CAAC;IAErD,OAAO,CAAC,GAAG,CAAC,uBAAuB,KAAK,gBAAgB,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;IAEjF,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAC9B,SAAS,CAAC,WAAW,CAAC;gBACtB,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC7B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CACxF;aACJ,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,8BAA8B,QAAQ,eAAe,OAAO,GAAG,CAAC,CAAC;YAE7F,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,MAAM;gBACZ,QAAQ;gBACR,KAAK;gBACL,OAAO;aACV,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAGxC,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClG,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjG,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE3G,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;gBAC1B,OAAO,CAAC,KAAK,CAAC,WAAW,KAAK,6BAA6B,WAAW,YAAY,EAAE;oBAChF,KAAK,EAAE,SAAS,CAAC,OAAO;oBACxB,QAAQ;oBACR,WAAW;oBACX,SAAS;oBACT,cAAc;oBACd,SAAS,EAAE,WAAW,CAAC,SAAS;iBACnC,CAAC,CAAC;gBAEH,OAAO;oBACH,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,SAAS;oBAChB,QAAQ;oBACR,KAAK;oBACL,OAAO;iBACV,CAAC;YACN,CAAC;YAGD,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAGnE,IAAI,WAAW,EAAE,CAAC;gBACd,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACnC,CAAC;YAED,OAAO,CAAC,IAAI,CAAC,YAAY,KAAK,oBAAoB,OAAO,IAAI,WAAW,IAAI,EAAE;gBAC1E,KAAK,EAAE,SAAS,CAAC,OAAO;gBACxB,QAAQ;gBACR,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;aACvG,CAAC,CAAC;YAGH,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAGD,OAAO;QACH,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,CAAC,eAAe,CAAC;QAC9C,QAAQ,EAAE,CAAC;QACX,KAAK;QACL,OAAO,EAAE,WAAW;KACvB,CAAC;AACN,CAAC;AAKD,SAAgB,mBAAmB,CAC/B,OAA+C;IAE/C,MAAM,YAAY,GAAuB,EAAE,CAAC;IAE5C,OAAO;SACF,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC;SAC/C,OAAO,CAAC,MAAM,CAAC,EAAE;QACd,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;IACL,CAAC,CAAC,CAAC;IAGP,YAAY,CAAC,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE;QACjC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACxB,CAAC;AAQM,KAAK,UAAU,oBAAoB,CACtC,SAAuD,EACvD,eAAgC,EAChC,UAAqC,EAAE;IAEvC,MAAM,EAAE,SAAS,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;IAGlC,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC/E,CAAC;IAED,MAAM,WAAW,GAAG,mBAAmB,CACnC,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,OAAO,CAC1B,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,mCAAmC,WAAW,CAAC,MAAM,yBAAyB,SAAS,EAAE,CAAC,CAAC;IAEvG,IAAI,WAAW,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QAElC,OAAO,uBAAuB,CAAC,SAAS,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAID,MAAM,UAAU,GAA0B,EAAE,CAAC;IAC7C,MAAM,SAAS,GAA2C,EAAE,CAAC;IAC7D,IAAI,aAAa,GAAG,CAAC,CAAC;IAGtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;QACrD,MAAM,KAAK,GAAQ,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;QACvD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACtC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;QAIjD,MAAM,gBAAgB,GAAoB;YACtC,GAAG,eAAe;YAClB,SAAS,EAAE,UAAU;YACrB,OAAO,EAAE,QAAQ;SACpB,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,uBAAuB,CAC7C,SAAS,EACT,gBAAgB,EAChB,EAAE,GAAG,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAChF,CAAC;QAEF,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,SAAS,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QACtC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC;QAG3C,IAAI,CAAC,GAAG,SAAS,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAC9D,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAE7D,OAAO;QACH,OAAO,EAAE,UAAU;QACnB,aAAa;QACb,YAAY;QACZ,UAAU;QACV,MAAM,EAAE,SAAS;KACpB,CAAC;AACN,CAAC"}