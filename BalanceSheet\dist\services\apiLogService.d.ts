export interface ApiLogData {
    userId?: string;
    companyId: string;
    method?: string;
    apiUrl?: string;
    status?: string;
    integrationName?: string;
    duration?: string;
    apiName: string;
    apiRequest?: any;
    apiResponse?: any;
}
export declare function logApiCall(logData: ApiLogData): Promise<void>;
export declare function logSuccessfulApiCall(companyId: string, method: string, apiUrl: string, duration: number, apiName: string, request?: any, response?: any, userId?: string): Promise<void>;
export declare function logFailedApiCall(companyId: string, method: string, apiUrl: string, duration: number, apiName: string, error: any, request?: any, userId?: string): Promise<void>;
export declare function getRecentApiLogs(companyId: string, limit?: number): Promise<{
    Id: string;
    UserId: string | null;
    CreatedAt: Date;
    Method: string | null;
    Status: string | null;
    Duration: string | null;
    CompanyId: string;
    ApiUrl: string | null;
    IntegrationName: string | null;
    ApiName: string;
    ApiRequest: import("@prisma/client/runtime/library").JsonValue | null;
    ApiResponse: import("@prisma/client/runtime/library").JsonValue | null;
    IsActive: boolean;
}[]>;
//# sourceMappingURL=apiLogService.d.ts.map