import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketEncryptionRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketEncryptionCommandInput
  extends PutBucketEncryptionRequest {}
export interface PutBucketEncryptionCommandOutput extends __MetadataBearer {}
declare const PutBucketEncryptionCommand_base: {
  new (
    input: PutBucketEncryptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketEncryptionCommandInput,
    PutBucketEncryptionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutBucketEncryptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketEncryptionCommandInput,
    PutBucketEncryptionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketEncryptionCommand extends PutBucketEncryptionCommand_base {
  protected static __types: {
    api: {
      input: PutBucketEncryptionRequest;
      output: {};
    };
    sdk: {
      input: PutBucketEncryptionCommandInput;
      output: PutBucketEncryptionCommandOutput;
    };
  };
}
