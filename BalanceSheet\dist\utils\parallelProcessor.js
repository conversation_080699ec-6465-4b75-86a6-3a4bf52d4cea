"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateMonthRanges = generateMonthRanges;
exports.processMonthsInParallel = processMonthsInParallel;
exports.combineMonthResults = combineMonthResults;
exports.processBatchedMonths = processBatchedMonths;
const p_limit_1 = __importDefault(require("p-limit"));
const moment_1 = __importDefault(require("moment"));
function generateMonthRanges(startDate, endDate) {
    const start = (0, moment_1.default)(startDate);
    const end = (0, moment_1.default)(endDate);
    const ranges = [];
    let current = start.clone().startOf('month');
    while (current.isSameOrBefore(end, 'month')) {
        const monthStart = current.clone();
        const monthEnd = current.clone().endOf('month');
        if (monthStart.isBefore(start)) {
            monthStart.set({
                date: start.date(),
                hour: start.hour(),
                minute: start.minute(),
                second: start.second()
            });
        }
        if (monthEnd.isAfter(end)) {
            monthEnd.set({
                date: end.date(),
                hour: end.hour(),
                minute: end.minute(),
                second: end.second()
            });
        }
        ranges.push({
            startDate: monthStart.format('YYYY-MM-DD'),
            endDate: monthEnd.format('YYYY-MM-DD'),
            month: current.format('YYYY-MM')
        });
        current.add(1, 'month');
    }
    return ranges;
}
async function processMonthsInParallel(processor, baseRequestData, options = {}) {
    const { concurrency = 2, retryAttempts = 3, retryDelay = 1000, timeout = 45000 } = options;
    if (!baseRequestData.startDate || !baseRequestData.endDate) {
        throw new Error('startDate and endDate are required for parallel processing');
    }
    const monthRanges = generateMonthRanges(baseRequestData.startDate, baseRequestData.endDate);
    console.log(`🚀 Starting parallel processing of ${monthRanges.length} months with concurrency: ${concurrency}`);
    console.log(`⚙️ Configuration: retries=${retryAttempts}, timeout=${timeout}ms, delay=${retryDelay}ms`);
    const limit = (0, p_limit_1.default)(concurrency);
    const startTime = Date.now();
    const results = [];
    const tasks = monthRanges.map((range) => limit(async () => {
        const monthRequestData = {
            ...baseRequestData,
            startDate: range.startDate,
            endDate: range.endDate
        };
        return processWithRetry(processor, monthRequestData, range.month, retryAttempts, retryDelay, timeout);
    }));
    const taskResults = await Promise.allSettled(tasks);
    taskResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
            results.push(result.value);
        }
        else {
            results.push({
                success: false,
                error: result.reason instanceof Error ? result.reason : new Error(String(result.reason)),
                duration: 0,
                month: monthRanges[index].month,
                attempt: 1
            });
        }
    });
    const totalDuration = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;
    const errors = results
        .filter(r => !r.success)
        .map(r => ({ month: r.month, error: r.error }));
    return {
        results,
        totalDuration,
        successCount,
        errorCount,
        errors
    };
}
async function processWithRetry(processor, requestData, month, maxAttempts, retryDelay, timeout) {
    let lastError = null;
    const monthKey = `${requestData.companyId}-${month}`;
    console.log(`🔄 Processing month ${month} for company ${requestData.companyId}`);
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        const startTime = Date.now();
        try {
            const result = await Promise.race([
                processor(requestData),
                new Promise((_, reject) => setTimeout(() => reject(new Error(`Processing timeout after ${timeout}ms`)), timeout))
            ]);
            const duration = Date.now() - startTime;
            console.log(`✅ Month ${month} processed successfully in ${duration}ms (attempt ${attempt})`);
            return {
                success: true,
                data: result,
                duration,
                month,
                attempt
            };
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));
            const duration = Date.now() - startTime;
            const isRateLimit = lastError.message.includes('rate limit') || lastError.message.includes('429');
            const isTimeout = lastError.message.includes('timeout') || lastError.message.includes('Timeout');
            const isNetworkError = lastError.message.includes('ECONNRESET') || lastError.message.includes('ETIMEDOUT');
            if (attempt === maxAttempts) {
                console.error(`❌ Month ${month} failed permanently after ${maxAttempts} attempts:`, {
                    error: lastError.message,
                    duration,
                    isRateLimit,
                    isTimeout,
                    isNetworkError,
                    companyId: requestData.companyId
                });
                return {
                    success: false,
                    error: lastError,
                    duration,
                    month,
                    attempt
                };
            }
            let delay = Math.min(retryDelay * Math.pow(2, attempt - 1), 30000);
            if (isRateLimit) {
                delay = Math.max(delay, 60000);
            }
            console.warn(`⚠️ Month ${month} failed (attempt ${attempt}/${maxAttempts}):`, {
                error: lastError.message,
                duration,
                nextRetryIn: delay,
                errorType: isRateLimit ? 'RATE_LIMIT' : isTimeout ? 'TIMEOUT' : isNetworkError ? 'NETWORK' : 'OTHER'
            });
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    return {
        success: false,
        error: lastError || new Error('Unknown error'),
        duration: 0,
        month,
        attempt: maxAttempts
    };
}
function combineMonthResults(results) {
    const combinedData = [];
    results
        .filter(result => result.success && result.data)
        .forEach(result => {
        if (result.data) {
            combinedData.push(...result.data);
        }
    });
    combinedData.sort((a, b) => {
        const dateA = new Date(a.date || '');
        const dateB = new Date(b.date || '');
        return dateA.getTime() - dateB.getTime();
    });
    return combinedData;
}
async function processBatchedMonths(processor, baseRequestData, options = {}) {
    const { batchSize = 6 } = options;
    if (!baseRequestData.startDate || !baseRequestData.endDate) {
        throw new Error('startDate and endDate are required for batch processing');
    }
    const monthRanges = generateMonthRanges(baseRequestData.startDate, baseRequestData.endDate);
    console.log(`📦 Starting batch processing of ${monthRanges.length} months in batches of ${batchSize}`);
    if (monthRanges.length <= batchSize) {
        return processMonthsInParallel(processor, baseRequestData, options);
    }
    const allResults = [];
    const allErrors = [];
    let totalDuration = 0;
    for (let i = 0; i < monthRanges.length; i += batchSize) {
        const batch = monthRanges.slice(i, i + batchSize);
        const batchStart = batch[0].startDate;
        const batchEnd = batch[batch.length - 1].endDate;
        const batchRequestData = {
            ...baseRequestData,
            startDate: batchStart,
            endDate: batchEnd
        };
        const batchResult = await processMonthsInParallel(processor, batchRequestData, { ...options, concurrency: Math.min(options.concurrency || 3, batch.length) });
        allResults.push(...batchResult.results);
        allErrors.push(...batchResult.errors);
        totalDuration += batchResult.totalDuration;
        if (i + batchSize < monthRanges.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    const successCount = allResults.filter(r => r.success).length;
    const errorCount = allResults.filter(r => !r.success).length;
    return {
        results: allResults,
        totalDuration,
        successCount,
        errorCount,
        errors: allErrors
    };
}
//# sourceMappingURL=parallelProcessor.js.map