import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  ListCodeSigningConfigsRequest,
  ListCodeSigningConfigsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListCodeSigningConfigsCommandInput
  extends ListCodeSigningConfigsRequest {}
export interface ListCodeSigningConfigsCommandOutput
  extends ListCodeSigningConfigsResponse,
    __MetadataBearer {}
declare const ListCodeSigningConfigsCommand_base: {
  new (
    input: ListCodeSigningConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCodeSigningConfigsCommandInput,
    ListCodeSigningConfigsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListCodeSigningConfigsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListCodeSigningConfigsCommandInput,
    ListCodeSigningConfigsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListCodeSigningConfigsCommand extends ListCodeSigningConfigsCommand_base {
  protected static __types: {
    api: {
      input: ListCodeSigningConfigsRequest;
      output: ListCodeSigningConfigsResponse;
    };
    sdk: {
      input: ListCodeSigningConfigsCommandInput;
      output: ListCodeSigningConfigsCommandOutput;
    };
  };
}
