import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { Concurrency, PutFunctionConcurrencyRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface PutFunctionConcurrencyCommandInput
  extends PutFunctionConcurrencyRequest {}
export interface PutFunctionConcurrencyCommandOutput
  extends Concurrency,
    __MetadataBearer {}
declare const PutFunctionConcurrencyCommand_base: {
  new (
    input: PutFunctionConcurrencyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutFunctionConcurrencyCommandInput,
    PutFunctionConcurrencyCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutFunctionConcurrencyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutFunctionConcurrencyCommandInput,
    PutFunctionConcurrencyCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutFunctionConcurrencyCommand extends PutFunctionConcurrencyCommand_base {
  protected static __types: {
    api: {
      input: PutFunctionConcurrencyRequest;
      output: Concurrency;
    };
    sdk: {
      input: PutFunctionConcurrencyCommandInput;
      output: PutFunctionConcurrencyCommandOutput;
    };
  };
}
