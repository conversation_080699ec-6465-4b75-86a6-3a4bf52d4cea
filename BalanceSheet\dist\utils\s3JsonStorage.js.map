{"version": 3, "file": "s3JsonStorage.js", "sourceRoot": "", "sources": ["../../src/utils/s3JsonStorage.ts"], "names": [], "mappings": ";;;;;;AAuRA,0DAKC;AAKD,oEASC;AAzRD,kDAAgE;AAChE,2BAAoC;AACpC,gDAAwB;AACxB,oDAA4B;AAC5B,uDAA0F;AAM1F,MAAM,SAAS,GAAG;IAEd,iBAAiB,EAAE,yBAAyB;IAG5C,SAAS,EAAE,oBAAoB;IAG/B,oBAAoB,EAAE,eAAe;IACrC,mBAAmB,EAAE,SAAS;IAG9B,WAAW,EAAE,CAAC;CACjB,CAAC;AAmBF,MAAa,oBAAoB;IAI7B;QAHQ,aAAQ,GAAoB,IAAI,CAAC;QAIrC,MAAM,MAAM,GAAG,IAAA,kCAAoB,GAAE,CAAC;QACtC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;QAG9C,IAAI,CAAC,IAAA,2BAAa,GAAE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,IAAA,0BAAY,GAAE,CAAC;YAGjC,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;gBACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAQ,CAAC;oBACzB,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,WAAW,EAAE;wBACT,WAAW,EAAE,SAAS,CAAC,WAAW;wBAClC,eAAe,EAAE,SAAS,CAAC,eAAe;qBAC7C;iBACJ,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YAChF,CAAC;QACL,CAAC;IACL,CAAC;IAWD,KAAK,CAAC,gBAAgB,CAClB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,YAAgC,EAChC,WAA8C;QAE9C,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,cAAc,SAAS,GAAG,CAAC,CAAC;QAE5E,IAAI,CAAC;YAED,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAGzC,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;YAC/E,CAAC;YAGD,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7E,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,2BAA2B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACxH,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,aAAa,CACvB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,IAA4D,EAC5D,QAAgC;QAEhC,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAG7D,MAAM,QAAQ,GAA4B;YACtC,SAAS;YACT,IAAI;YACJ,KAAK;YACL,QAAQ;YACR,WAAW,EAAE,IAAA,gBAAM,GAAE,CAAC,WAAW,EAAE;YACnC,QAAQ;YACR,WAAW,EAAE,IAAI,CAAC,MAAM;YACxB,IAAI;SACP,CAAC;QAGF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACzE,MAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAEnE,IAAI,CAAC;YAED,MAAM,aAAE,CAAC,SAAS,CACd,aAAa,EACb,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,EACrD,MAAM,CACT,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,+BAA+B,aAAa,EAAE,CAAC,CAAC;YAG5D,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAGtD,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,GAAG,CAAC,iEAAiE,QAAQ,EAAE,CAAC,CAAC;YAC7F,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,QAAQ,aAAa,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,UAAU,CACpB,aAAqB,EACrB,KAAa,EACb,QAAiC;QAEjC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAErD,MAAM,aAAa,GAAG,IAAI,4BAAgB,CAAC;gBACvC,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,KAAK;gBACV,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,kBAAkB;gBAC/B,QAAQ,EAAE;oBACN,YAAY,EAAE,QAAQ,CAAC,SAAS;oBAChC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAChC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;oBAClC,WAAW,EAAE,QAAQ,CAAC,QAAQ;oBAC9B,cAAc,EAAE,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE;oBAC/C,cAAc,EAAE,QAAQ,CAAC,WAAW;iBACvC;aACJ,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACrG,CAAC;IACL,CAAC;IAKO,gBAAgB,CACpB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,QAAgC;QAEhC,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAC7D,MAAM,MAAM,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC;QACxG,OAAO,iBAAiB,SAAS,IAAI,QAAQ,IAAI,MAAM,OAAO,CAAC;IACnE,CAAC;IAKO,aAAa,CACjB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,QAAgC;QAEhC,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;QAC7D,MAAM,MAAM,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC;QACxG,OAAO,GAAG,SAAS,CAAC,SAAS,IAAI,SAAS,IAAI,IAAI,IAAI,QAAQ,kBAAkB,MAAM,OAAO,CAAC;IAClG,CAAC;IAKO,KAAK,CAAC,2BAA2B;QACrC,IAAI,CAAC;YACD,MAAM,aAAE,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACD,MAAM,aAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CAAC,mCAAmC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAExE,CAAC;IACL,CAAC;CACJ;AAlND,oDAkNC;AAKD,IAAI,qBAAqB,GAAgC,IAAI,CAAC;AAK9D,SAAgB,uBAAuB;IACnC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACzB,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACvD,CAAC;IACD,OAAO,qBAAqB,CAAC;AACjC,CAAC;AAKM,KAAK,UAAU,4BAA4B,CAC9C,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,YAAgC,EAChC,WAA8C;IAE9C,MAAM,cAAc,GAAG,uBAAuB,EAAE,CAAC;IACjD,MAAM,cAAc,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;AAC7F,CAAC"}