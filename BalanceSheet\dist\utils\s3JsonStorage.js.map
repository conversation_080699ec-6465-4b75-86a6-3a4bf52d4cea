{"version": 3, "file": "s3JsonStorage.js", "sourceRoot": "", "sources": ["../../src/utils/s3JsonStorage.ts"], "names": [], "mappings": ";;;;;AA2RA,oEAQC;AAlRD,kDAAgE;AAChE,2BAAoC;AACpC,gDAAwB;AACxB,oDAA4B;AAC5B,uDAA0F;AAM1F,MAAM,SAAS,GAAG;IAEd,iBAAiB,EAAE,yBAAyB;IAG5C,SAAS,EAAE,oBAAoB;IAG/B,oBAAoB,EAAE,eAAe;IACrC,mBAAmB,EAAE,SAAS;IAG9B,WAAW,EAAE,CAAC;CACjB,CAAC;AA2BF,SAAS,kBAAkB;IACvB,MAAM,MAAM,GAAG,IAAA,kCAAoB,GAAE,CAAC;IACtC,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;IAG/C,IAAI,CAAC,IAAA,2BAAa,GAAE,IAAI,UAAU,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,IAAA,0BAAY,GAAE,CAAC;QAGjC,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;YACrD,MAAM,MAAM,GAAG,IAAI,oBAAQ,CAAC;gBACxB,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,WAAW,EAAE;oBACT,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,eAAe,EAAE,SAAS,CAAC,eAAe;iBAC7C;aACJ,CAAC,CAAC;YACH,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;QAClC,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAED,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;AACxC,CAAC;AAWD,KAAK,UAAU,gBAAgB,CAC3B,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,YAAgC,EAChC,WAA8C;IAE9C,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,cAAc,SAAS,GAAG,CAAC,CAAC;IAE5E,IAAI,CAAC;QAED,MAAM,2BAA2B,EAAE,CAAC;QAGpC,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,MAAM,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;QAC1E,CAAC;QAGD,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;IACnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACrE,MAAM,IAAI,KAAK,CAAC,2BAA2B,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;IACxH,CAAC;AACL,CAAC;AAKD,KAAK,UAAU,aAAa,CACxB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,IAA4D,EAC5D,QAAgC;IAEhC,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC7D,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IAGtC,MAAM,QAAQ,GAA4B;QACtC,SAAS;QACT,IAAI;QACJ,KAAK;QACL,QAAQ;QACR,WAAW,EAAE,IAAA,gBAAM,GAAE,CAAC,WAAW,EAAE;QACnC,QAAQ;QACR,WAAW,EAAE,IAAI,CAAC,MAAM;QACxB,IAAI;KACP,CAAC;IAGF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACpE,MAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;IACvE,MAAM,KAAK,GAAG,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE9D,IAAI,CAAC;QAED,MAAM,aAAE,CAAC,SAAS,CACd,aAAa,EACb,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC,WAAW,CAAC,EACrD,MAAM,CACT,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,+BAA+B,aAAa,EAAE,CAAC,CAAC;QAG5D,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YACzC,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAGvF,MAAM,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,iEAAiE,QAAQ,EAAE,CAAC,CAAC;QAC7F,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,QAAQ,aAAa,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,KAAK,CAAC;IAChB,CAAC;AACL,CAAC;AAKD,KAAK,UAAU,UAAU,CACrB,QAAkB,EAClB,UAAkB,EAClB,aAAqB,EACrB,KAAa,EACb,QAAiC;IAEjC,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAErD,MAAM,aAAa,GAAG,IAAI,4BAAgB,CAAC;YACvC,MAAM,EAAE,UAAU;YAClB,GAAG,EAAE,KAAK;YACV,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,kBAAkB;YAC/B,QAAQ,EAAE;gBACN,YAAY,EAAE,QAAQ,CAAC,SAAS;gBAChC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAClC,WAAW,EAAE,QAAQ,CAAC,QAAQ;gBAC9B,cAAc,EAAE,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE;gBAC/C,cAAc,EAAE,QAAQ,CAAC,WAAW;aACvC;SACJ,CAAC,CAAC;QAEH,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;IAC5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;IACrG,CAAC;AACL,CAAC;AAKD,SAAS,gBAAgB,CACrB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,QAAgC;IAEhC,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC7D,MAAM,MAAM,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC;IACxG,OAAO,iBAAiB,SAAS,IAAI,QAAQ,IAAI,MAAM,OAAO,CAAC;AACnE,CAAC;AAKD,SAAS,aAAa,CAClB,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,QAAgC;IAEhC,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IAC7D,MAAM,MAAM,GAAG,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC;IACxG,OAAO,GAAG,SAAS,CAAC,SAAS,IAAI,SAAS,IAAI,IAAI,IAAI,QAAQ,kBAAkB,MAAM,OAAO,CAAC;AAClG,CAAC;AAKD,KAAK,UAAU,2BAA2B;IACtC,IAAI,CAAC;QACD,MAAM,aAAE,CAAC,KAAK,CAAC,SAAS,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAClE,MAAM,KAAK,CAAC;IAChB,CAAC;AACL,CAAC;AAKD,KAAK,UAAU,gBAAgB,CAAC,QAAgB;IAC5C,IAAI,CAAC;QACD,MAAM,aAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;IAC1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,IAAI,CAAC,mCAAmC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;IAExE,CAAC;AACL,CAAC;AAKM,KAAK,UAAU,4BAA4B,CAC9C,SAAiB,EACjB,IAAY,EACZ,KAAa,EACb,YAAgC,EAChC,WAA8C;IAE9C,MAAM,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;AAC9E,CAAC"}