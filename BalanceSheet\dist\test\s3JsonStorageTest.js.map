{"version": 3, "file": "s3JsonStorageTest.js", "sourceRoot": "", "sources": ["../../src/test/s3JsonStorageTest.ts"], "names": [], "mappings": ";;AA8KS,8CAAiB;AAAE,4DAAwB;AAAE,0DAAuB;AAlK7E,0DAAsE;AAMtE,SAAS,wBAAwB;IAC7B,OAAO;QACH;YACI,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,cAAc;YAC3B,MAAM,EAAE,QAAQ;YAChB,mBAAmB,EAAE,WAAW;YAChC,mBAAmB,EAAE,WAAW;SACnC;QACD;YACI,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,qBAAqB;YAClC,MAAM,EAAE,OAAO;YACf,mBAAmB,EAAE,WAAW;YAChC,mBAAmB,EAAE,IAAI;SAC5B;QACD;YACI,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,WAAW;YACxB,MAAM,EAAE,QAAQ;YAChB,mBAAmB,EAAE,IAAI;YACzB,mBAAmB,EAAE,WAAW;SACnC;KACJ,CAAC;AACN,CAAC;AAKD,SAAS,uBAAuB;IAC5B,OAAO;QACH;YACI,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,cAAc;YAC3B,MAAM,EAAE,QAAQ;SACnB;QACD;YACI,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,qBAAqB;YAClC,MAAM,EAAE,OAAO;SAClB;QACD;YACI,SAAS,EAAE,kBAAkB;YAC7B,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,WAAW;YACxB,MAAM,EAAE,QAAQ;SACnB;KACJ,CAAC;AACN,CAAC;AAKD,KAAK,UAAU,iBAAiB;IAC5B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,IAAI,CAAC;QAED,MAAM,SAAS,GAAG,kBAAkB,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,MAAM,KAAK,GAAG,CAAC,CAAC;QAEhB,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,EAAE,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,IAAI,CAAC,CAAC;QAGpC,MAAM,YAAY,GAAG,wBAAwB,EAAE,CAAC;QAChD,MAAM,WAAW,GAAG,uBAAuB,EAAE,CAAC;QAE9C,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,wBAAwB,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,uBAAuB,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;QAG3D,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,MAAM,IAAA,4CAA4B,EAC9B,SAAS,EACT,IAAI,EACJ,KAAK,EACL,YAAY,EACZ,WAAW,CACd,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAEnD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAClD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAErB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACrC,OAAO,CAAC,KAAK,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9C,OAAO,CAAC,KAAK,CAAC,aAAa,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEhD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC;AAKD,SAAS,sBAAsB;IAC3B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,SAAS,EAAE,CAAC,CAAC;IACjE,OAAO,CAAC,GAAG,CAAC,kBAAkB,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC,CAAC;IACrE,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;IAC7D,OAAO,CAAC,GAAG,CAAC,sBAAsB,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,SAAS,EAAE,CAAC,CAAC;IAC7E,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;IAC/F,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC;AAC7G,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAE1B,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;IAE3B,sBAAsB,EAAE,CAAC;IACzB,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QAChC,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC"}