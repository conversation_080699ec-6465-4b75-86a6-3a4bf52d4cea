import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  FunctionConfiguration,
  UpdateFunctionCodeRequest,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface UpdateFunctionCodeCommandInput
  extends UpdateFunctionCodeRequest {}
export interface UpdateFunctionCodeCommandOutput
  extends FunctionConfiguration,
    __MetadataBearer {}
declare const UpdateFunctionCodeCommand_base: {
  new (
    input: UpdateFunctionCodeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateFunctionCodeCommandInput,
    UpdateFunctionCodeCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateFunctionCodeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateFunctionCodeCommandInput,
    UpdateFunctionCodeCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateFunctionCodeCommand extends UpdateFunctionCodeCommand_base {
  protected static __types: {
    api: {
      input: UpdateFunctionCodeRequest;
      output: FunctionConfiguration;
    };
    sdk: {
      input: UpdateFunctionCodeCommandInput;
      output: UpdateFunctionCodeCommandOutput;
    };
  };
}
