/**
 * Sync Logging Service
 * 
 * Handles logging of synchronization processes to the SyncLog table for tracking and monitoring
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';
dotenv.config();

/**
 * Sync Status enum matching Prisma schema
 */
export enum SyncStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    SUCCESS = 'SUCCESS',
    WARNING = 'WARNING',
    ERROR = 'ERROR',
    RETRYING = 'RETRYING',
    CANCELLED = 'CANCELLED'
}

let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

/**
 * Sync log entry data structure
 */
export interface SyncLogData {
    requestId?: string;
    entity: string;
    integration: string;
    apiEndpoint?: string;
    method?: string;
    companyId: string;
    userId?: string;
    requestPayload?: any;
    maxRetries?: number;
}

/**
 * Sync log update data structure
 */
export interface SyncLogUpdate {
    Status?: SyncStatus;
    Message?: string;
    Duration?: string;
    ResponsePayload?: any;
    ErrorDetails?: any;
    RetryCount?: number;
    LastRetryAt?: Date;
    NextRetryAt?: Date;
    CompletedAt?: Date;
    ApiEndpoint?: string;
    Method?: string;
}

/**
 * Create a new sync log entry
 * 
 * @param logData - Sync log data
 * @returns Promise<string> - Returns the sync log ID
 */
export async function createSyncLog(logData: SyncLogData): Promise<string> {
    try {
        const requestId = logData.requestId || uuidv4();

        console.log(`📝 Creating sync log for ${logData.entity} - Request ID: ${requestId}`);

        const syncLog = await getPrismaClient().syncLog.create({
            data: {
                RequestId: requestId,
                Entity: logData.entity,
                Integration: logData.integration,
                ApiEndpoint: logData.apiEndpoint || null,
                Method: logData.method || null,
                Status: SyncStatus.PENDING,
                CompanyId: logData.companyId,
                UserId: logData.userId || null,
                RequestPayload: logData.requestPayload || null,
                MaxRetries: logData.maxRetries || 3,
                StartedAt: new Date(),
            },
        });

        console.log(`✅ Sync log created with ID: ${syncLog.Id}`);
        return syncLog.Id;
    } catch (error: any) {
        console.error('❌ Failed to create sync log:', {
            error: error.message,
            entity: logData.entity,
            companyId: logData.companyId,
        });
        throw error;
    }
}

/**
 * Update an existing sync log entry
 * 
 * @param syncLogId - Sync log ID
 * @param updateData - Data to update
 */
export async function updateSyncLog(syncLogId: string, updateData: SyncLogUpdate): Promise<void> {
    try {
        console.log(`📝 Updating sync log ${syncLogId} - Status: ${updateData.Status}`);

        await getPrismaClient().syncLog.update({
            where: { Id: syncLogId },
            data: {
                ...(updateData.Status && { Status: updateData.Status }),
                ...(updateData.Message && { Message: updateData.Message }),
                ...(updateData.Duration && { Duration: updateData.Duration }),
                ...(updateData.ResponsePayload && { ResponsePayload: updateData.ResponsePayload }),
                ...(updateData.ErrorDetails && { ErrorDetails: updateData.ErrorDetails }),
                ...(updateData.RetryCount !== undefined && { RetryCount: updateData.RetryCount }),
                ...(updateData.LastRetryAt && { LastRetryAt: updateData.LastRetryAt }),
                ...(updateData.NextRetryAt && { NextRetryAt: updateData.NextRetryAt }),
                ...(updateData.CompletedAt && { CompletedAt: updateData.CompletedAt }),
                ...(updateData.ApiEndpoint && { ApiEndpoint: updateData.ApiEndpoint }),
                ...(updateData.Method && { Method: updateData.Method }),
            },
        });

        console.log(`✅ Sync log ${syncLogId} updated successfully`);
    } catch (error: any) {
        console.error('❌ Failed to update sync log:', {
            error: error.message,
            syncLogId,
            status: updateData.Status,
        });
        throw error;
    }
}

/**
 * Mark sync as started
 * 
 * @param syncLogId - Sync log ID
 * @param apiEndpoint - API endpoint being called
 * @param method - HTTP method
 */
export async function markSyncStarted(
    syncLogId: string,
    apiEndpoint?: string,
    method?: string
): Promise<void> {
    const updateData: SyncLogUpdate = {
        Status: SyncStatus.IN_PROGRESS,
        Message: 'Sync process started',
    };

    if (apiEndpoint) {
        updateData.ApiEndpoint = apiEndpoint;
    }

    if (method) {
        updateData.Method = method;
    }

    await updateSyncLog(syncLogId, updateData);
}

/**
 * Mark sync as successful
 * 
 * @param syncLogId - Sync log ID
 * @param duration - Process duration in milliseconds
 * @param message - Success message
 * @param responsePayload - Response data
 */
export async function markSyncSuccess(
    syncLogId: string,
    duration: number,
    message?: string,
    responsePayload?: any
): Promise<void> {
    await updateSyncLog(syncLogId, {
        Status: SyncStatus.SUCCESS,
        Message: message || 'Sync completed successfully',
        Duration: `${duration}ms`,
        ResponsePayload: responsePayload,
        CompletedAt: new Date(),
    });
}

/**
 * Mark sync as failed
 * 
 * @param syncLogId - Sync log ID
 * @param duration - Process duration in milliseconds
 * @param error - Error object
 * @param shouldRetry - Whether this sync should be retried
 */
export async function markSyncFailed(
    syncLogId: string,
    duration: number,
    error: any,
    shouldRetry: boolean = false
): Promise<void> {
    const errorDetails = {
        message: error.message,
        stack: error.stack,
        ...(error.response && {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
        }),
    };

    const updateData: SyncLogUpdate = {
        Status: shouldRetry ? SyncStatus.RETRYING : SyncStatus.ERROR,
        Message: `Sync failed: ${error.message}`,
        Duration: `${duration}ms`,
        ErrorDetails: errorDetails,
    };

    if (!shouldRetry) {
        updateData.CompletedAt = new Date();
    }

    if (shouldRetry) {
        updateData.LastRetryAt = new Date();
        updateData.NextRetryAt = new Date(Date.now() + 60000); // Retry after 1 minute
    }

    await updateSyncLog(syncLogId, updateData);
}

/**
 * Mark sync for retry
 * 
 * @param syncLogId - Sync log ID
 * @param retryCount - Current retry count
 * @param nextRetryAt - Next retry timestamp
 */
export async function markSyncForRetry(
    syncLogId: string,
    retryCount: number,
    nextRetryAt: Date
): Promise<void> {
    await updateSyncLog(syncLogId, {
        Status: SyncStatus.RETRYING,
        Message: `Retry attempt ${retryCount}`,
        RetryCount: retryCount,
        LastRetryAt: new Date(),
        NextRetryAt: nextRetryAt,
    });
}

/**
 * Get sync logs for a company
 * 
 * @param companyId - Company ID
 * @param entity - Optional entity filter
 * @param limit - Number of logs to retrieve (default: 50)
 * @returns Promise<SyncLog[]> - Sync logs
 */
export async function getSyncLogs(
    companyId: string,
    entity?: string,
    limit: number = 50
) {
    try {
        const logs = await getPrismaClient().syncLog.findMany({
            where: {
                CompanyId: companyId,
                ...(entity && { Entity: entity }),
            },
            orderBy: {
                CreatedAt: 'desc',
            },
            take: limit,
        });

        return logs;
    } catch (error: any) {
        console.error('❌ Failed to retrieve sync logs:', {
            error: error.message,
            companyId,
            entity,
        });
        return [];
    }
}
