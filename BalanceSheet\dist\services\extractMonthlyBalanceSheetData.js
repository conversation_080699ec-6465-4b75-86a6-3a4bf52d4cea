"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractMonthlyBalanceSheetData = extractMonthlyBalanceSheetData;
exports.extractMonthlyBalanceSheetDataWithoutTracking = extractMonthlyBalanceSheetDataWithoutTracking;
function extractMonthlyBalanceSheetData(report, trackingCategories, requestData, month) {
    const rows = report.Rows;
    const results = [];
    for (const row of rows) {
        if (row.RowType === 'Section' && row.Rows) {
            for (const innerRow of row.Rows) {
                if (innerRow.RowType === 'Row' && innerRow.Cells) {
                    const firstCell = innerRow.Cells[0];
                    const accountName = firstCell?.Value || null;
                    let accountId = null;
                    if (firstCell?.Attributes) {
                        const accAttr = firstCell.Attributes.find((a) => a.Id === 'account');
                        accountId = accAttr?.Value || null;
                    }
                    const amountCell = innerRow.Cells[1];
                    const amount = parseFloat(amountCell?.Value.replace(/,/g, '')) || 0;
                    let trackingCategory1 = null;
                    let trackingCategory2 = null;
                    if (innerRow.Cells.length > 2) {
                        const trackingNames = innerRow.Cells
                            .slice(2)
                            .map((c) => c.Value)
                            .filter((v) => !!v);
                        trackingNames.forEach((name) => {
                            trackingCategories.forEach((category) => {
                                category.options.forEach((option) => {
                                    if (option.name === name) {
                                        if (!trackingCategory1)
                                            trackingCategory1 = option.id;
                                        else
                                            trackingCategory2 = option.id;
                                    }
                                });
                            });
                        });
                    }
                    if (accountId) {
                        results.push({
                            Year: month.year,
                            Month: month.month,
                            AccountId: accountId,
                            AccountName: accountName,
                            Amount: amount,
                            TrackingCategoryId1: trackingCategory1,
                            TrackingCategoryId2: trackingCategory2,
                            CompanyId: requestData.companyId,
                        });
                    }
                }
            }
        }
    }
    return results;
}
function extractMonthlyBalanceSheetDataWithoutTracking(report, requestData, month) {
    const rows = report.Rows;
    const accountMap = new Map();
    for (const row of rows) {
        if (row.RowType === 'Section' && row.Rows) {
            for (const innerRow of row.Rows) {
                if (innerRow.RowType === 'Row' && innerRow.Cells) {
                    const firstCell = innerRow.Cells[0];
                    const accountName = firstCell?.Value || null;
                    let accountId = null;
                    if (firstCell?.Attributes) {
                        const accAttr = firstCell.Attributes.find((a) => a.Id === 'account');
                        accountId = accAttr?.Value || null;
                    }
                    if (accountId && accountName) {
                        const amountCell = innerRow.Cells[1];
                        const amount = parseFloat(amountCell?.Value?.replace(/,/g, '')) || 0;
                        if (accountMap.has(accountId)) {
                            const existing = accountMap.get(accountId);
                            existing.totalAmount += amount;
                        }
                        else {
                            accountMap.set(accountId, {
                                accountName,
                                totalAmount: amount
                            });
                        }
                    }
                }
            }
        }
    }
    const results = [];
    for (const [accountId, data] of accountMap.entries()) {
        if (data.totalAmount !== 0) {
            results.push({
                Year: month.year,
                Month: month.month,
                AccountId: accountId,
                AccountName: data.accountName,
                Amount: data.totalAmount,
                CompanyId: requestData.companyId,
            });
        }
    }
    return results;
}
//# sourceMappingURL=extractMonthlyBalanceSheetData.js.map