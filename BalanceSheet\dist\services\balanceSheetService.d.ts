import { Context } from 'aws-lambda';
import { XeroRequestData } from '../types';
export declare function processBalanceSheetRequest(requestData: XeroRequestData, _context: Context): Promise<void>;
export declare function performHealthCheck(companyId: string): Promise<{
    healthy: boolean;
    checks: Record<string, {
        status: 'pass' | 'fail';
        message: string;
        duration?: number;
    }>;
}>;
export declare function getSyncStatistics(companyId: string): Promise<{
    lastSyncDate: string | null;
    totalMonthsTracking: number;
    totalMonthsSummary: number;
    dataIntegrity: {
        trackingVsSummaryMatch: boolean;
        missingMonths: string[];
    };
    performance: {
        avgProcessingTime: number | null;
        lastProcessingTime: number | null;
    };
}>;
//# sourceMappingURL=balanceSheetService.d.ts.map