/**
 * Simple JavaScript test for the integration logger
 * This bypasses TypeScript compilation issues
 */

const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

// Load environment variables
require('dotenv').config();

/**
 * Test database connection
 */
async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');

  const prisma = new PrismaClient();

  try {
    // Simple query to test connection
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Database connection successful');

    // Check for existing companies
    try {
      const companies = await prisma.$queryRaw`SELECT "Id", "Name" FROM "Company" LIMIT 5`;
      console.log('📋 Available companies:');
      companies.forEach(company => {
        console.log(`   ${company.Id}: ${company.Name}`);
      });

      // Store the first company ID for testing
      if (companies.length > 0) {
        global.testCompanyId = companies[0].Id;
        console.log(`✅ Using company ID for test: ${global.testCompanyId}`);
      }
    } catch (error) {
      console.log('⚠️ Could not fetch companies:', error.message);
    }

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Test integration log creation
 */
async function testIntegrationLogCreation() {
  console.log('🧪 Testing IntegrationLog creation...');

  const prisma = new PrismaClient();

  try {
    // Generate Request ID and use existing Company ID
    const requestId = uuidv4();
    const companyId = global.testCompanyId || uuidv4(); // Use existing company or fallback

    console.log(`📝 Generated Request ID: ${requestId}`);
    console.log(`📝 Using Company ID: ${companyId}`);

    if (!global.testCompanyId) {
      console.log('⚠️ No existing company found, test may fail due to foreign key constraint');
    }

    const logData = {
      // Id is auto-generated by Prisma (@default(uuid()))
      RequestId: requestId,
      CompanyId: companyId,
      ApiName: 'BalanceSheetSync',
      Method: 'POST',
      ApiUrl: '/xero/sync-balance-sheet',
      IntegrationName: 'Xero',
      StatusCode: '200',
      Duration: '2.5s',
      Message: 'Balance Sheet sync completed successfully',
      Entity: 'BalanceSheet',
      TriggeredBy: 'SYSTEM',
      SyncStatus: 'SUCCESS',
      SyncSummary: {
        totalMonths: 12,
        processedMonths: 12,
        trackingRecords: 1500,
        summaryRecords: 150,
        apiCalls: 24,
        errors: 0,
        warnings: 0,
        duration: '2.5s',
      },
      StartedAt: new Date(),
      CompletedAt: new Date(),
    };

    const log = await prisma.integrationLog.create({
      data: logData,
    });

    console.log(`✅ IntegrationLog created successfully with ID: ${log.Id}`);
    console.log(`📝 Request ID: ${log.RequestId}`);
    console.log(`🏢 Company: ${log.CompanyId}`);
    console.log(`📊 Status: ${log.SyncStatus}`);
    console.log(`⏱️ Duration: ${log.Duration}`);

    return true;
  } catch (error) {
    console.error('❌ IntegrationLog creation failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Test integration log query
 */
async function testIntegrationLogQuery() {
  console.log('🔍 Testing IntegrationLog query...');

  const prisma = new PrismaClient();

  try {
    const logs = await prisma.integrationLog.findMany({
      where: {
        ApiName: 'BalanceSheetSync',
      },
      orderBy: {
        StartedAt: 'desc',
      },
      take: 5,
    });

    console.log(`✅ Found ${logs.length} Balance Sheet logs`);

    logs.forEach((log, index) => {
      console.log(`📋 Log ${index + 1}:`);
      console.log(`   ID: ${log.Id}`);
      console.log(`   Company: ${log.CompanyId}`);
      console.log(`   Status: ${log.SyncStatus}`);
      console.log(`   Duration: ${log.Duration || 'N/A'}`);
      console.log(`   Started: ${log.StartedAt}`);
    });

    return true;
  } catch (error) {
    console.error('❌ IntegrationLog query failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Main test function
 */
async function runTest() {
  console.log('🚀 Starting Balance Sheet Integration Logger Test...\n');

  try {
    // Test database connection
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      throw new Error('Database connection failed');
    }
    console.log();

    // Test log creation
    const logCreated = await testIntegrationLogCreation();
    if (!logCreated) {
      throw new Error('Log creation failed');
    }
    console.log();

    // Test log query
    const logQueried = await testIntegrationLogQuery();
    if (!logQueried) {
      throw new Error('Log query failed');
    }
    console.log();

    console.log('✅ All tests passed! Integration logging is working correctly.');
    console.log('\n📋 What was tested:');
    console.log('   ✓ Database connection');
    console.log('   ✓ IntegrationLog model access');
    console.log('   ✓ Log creation with all fields');
    console.log('   ✓ Log querying and retrieval');
    console.log('   ✓ JSON data storage (SyncSummary)');
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);

    console.log('\n💡 Troubleshooting:');
    console.log('1. Check DATABASE_URL in .env file');
    console.log('2. Ensure database is running and accessible');
    console.log('3. Verify IntegrationLog table exists in database');
    console.log('4. Run: npx prisma generate');
    console.log('5. Check database permissions');

    process.exit(1);
  }
}

/**
 * Display environment information
 */
function displayEnvironmentInfo() {
  console.log('🌍 Environment Information:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`   DATABASE_URL: ${process.env.DATABASE_URL ? '***configured***' : 'not set'}`);
  console.log(`   Platform: ${process.platform}`);
  console.log(`   Node Version: ${process.version}\n`);
}

// Run the test
if (require.main === module) {
  displayEnvironmentInfo();
  runTest();
}

module.exports = {
  runTest,
  testDatabaseConnection,
  testIntegrationLogCreation,
  testIntegrationLogQuery,
};
