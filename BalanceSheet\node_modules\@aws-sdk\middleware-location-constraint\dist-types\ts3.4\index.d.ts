import {
  InitializeHandlerOptions,
  InitializeMiddleware,
  Pluggable,
} from "@smithy/types";
import { LocationConstraintResolvedConfig } from "./configuration";
export declare function locationConstraintMiddleware(
  options: LocationConstraintResolvedConfig
): InitializeMiddleware<any, any>;
export declare const locationConstraintMiddlewareOptions: InitializeHandlerOptions;
export declare const getLocationConstraintPlugin: (
  config: LocationConstraintResolvedConfig
) => Pluggable<any, any>;
