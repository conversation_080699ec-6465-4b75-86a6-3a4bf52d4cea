import { Command as $Command } from "@smithy/smithy-client";
import {
  BlobPayloadInputTypes,
  MetadataBearer as __MetadataBearer,
} from "@smithy/types";
import { Uint8ArrayBlobAdapter } from "@smithy/util-stream";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { InvocationRequest, InvocationResponse } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export type InvokeCommandInputType = Pick<
  InvocationRequest,
  Exclude<keyof InvocationRequest, "Payload">
> & {
  Payload?: BlobPayloadInputTypes;
};
export interface InvokeCommandInput extends InvokeCommandInputType {}
export type InvokeCommandOutputType = Pick<
  InvocationResponse,
  Exclude<keyof InvocationResponse, "Payload">
> & {
  Payload?: Uint8ArrayBlobAdapter;
};
export interface InvokeCommandOutput
  extends InvokeCommandOutputType,
    __MetadataBearer {}
declare const InvokeCommand_base: {
  new (input: InvokeCommandInput): import("@smithy/smithy-client").CommandImpl<
    InvokeCommandInput,
    InvokeCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (input: InvokeCommandInput): import("@smithy/smithy-client").CommandImpl<
    InvokeCommandInput,
    InvokeCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class InvokeCommand extends InvokeCommand_base {
  protected static __types: {
    api: {
      input: InvocationRequest;
      output: InvocationResponse;
    };
    sdk: {
      input: InvokeCommandInput;
      output: InvokeCommandOutput;
    };
  };
}
