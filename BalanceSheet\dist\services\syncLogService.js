"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SyncStatus = void 0;
exports.createSyncLog = createSyncLog;
exports.updateSyncLog = updateSyncLog;
exports.markSyncStarted = markSyncStarted;
exports.markSyncSuccess = markSyncSuccess;
exports.markSyncFailed = markSyncFailed;
exports.markSyncForRetry = markSyncForRetry;
exports.getSyncLogs = getSyncLogs;
const client_1 = require("@prisma/client");
const uuid_1 = require("uuid");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
var SyncStatus;
(function (SyncStatus) {
    SyncStatus["PENDING"] = "PENDING";
    SyncStatus["IN_PROGRESS"] = "IN_PROGRESS";
    SyncStatus["SUCCESS"] = "SUCCESS";
    SyncStatus["WARNING"] = "WARNING";
    SyncStatus["ERROR"] = "ERROR";
    SyncStatus["RETRYING"] = "RETRYING";
    SyncStatus["CANCELLED"] = "CANCELLED";
})(SyncStatus || (exports.SyncStatus = SyncStatus = {}));
let prisma = null;
function getPrismaClient() {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'],
            errorFormat: 'pretty',
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new client_1.PrismaClient(config);
    }
    return prisma;
}
async function createSyncLog(logData) {
    try {
        const requestId = logData.requestId || (0, uuid_1.v4)();
        console.log(`📝 Creating sync log for ${logData.entity} - Request ID: ${requestId}`);
        const syncLog = await getPrismaClient().syncLog.create({
            data: {
                RequestId: requestId,
                Entity: logData.entity,
                Integration: logData.integration,
                ApiEndpoint: logData.apiEndpoint || null,
                Method: logData.method || null,
                Status: SyncStatus.PENDING,
                CompanyId: logData.companyId,
                UserId: logData.userId || null,
                RequestPayload: logData.requestPayload || null,
                MaxRetries: logData.maxRetries || 3,
                StartedAt: new Date(),
            },
        });
        console.log(`✅ Sync log created with ID: ${syncLog.Id}`);
        return syncLog.Id;
    }
    catch (error) {
        console.error('❌ Failed to create sync log:', {
            error: error.message,
            entity: logData.entity,
            companyId: logData.companyId,
        });
        throw error;
    }
}
async function updateSyncLog(syncLogId, updateData) {
    try {
        console.log(`📝 Updating sync log ${syncLogId} - Status: ${updateData.Status}`);
        await getPrismaClient().syncLog.update({
            where: { Id: syncLogId },
            data: {
                ...(updateData.Status && { Status: updateData.Status }),
                ...(updateData.Message && { Message: updateData.Message }),
                ...(updateData.Duration && { Duration: updateData.Duration }),
                ...(updateData.ResponsePayload && { ResponsePayload: updateData.ResponsePayload }),
                ...(updateData.ErrorDetails && { ErrorDetails: updateData.ErrorDetails }),
                ...(updateData.RetryCount !== undefined && { RetryCount: updateData.RetryCount }),
                ...(updateData.LastRetryAt && { LastRetryAt: updateData.LastRetryAt }),
                ...(updateData.NextRetryAt && { NextRetryAt: updateData.NextRetryAt }),
                ...(updateData.CompletedAt && { CompletedAt: updateData.CompletedAt }),
                ...(updateData.ApiEndpoint && { ApiEndpoint: updateData.ApiEndpoint }),
                ...(updateData.Method && { Method: updateData.Method }),
            },
        });
        console.log(`✅ Sync log ${syncLogId} updated successfully`);
    }
    catch (error) {
        console.error('❌ Failed to update sync log:', {
            error: error.message,
            syncLogId,
            status: updateData.Status,
        });
        throw error;
    }
}
async function markSyncStarted(syncLogId, apiEndpoint, method) {
    const updateData = {
        Status: SyncStatus.IN_PROGRESS,
        Message: 'Sync process started',
    };
    if (apiEndpoint) {
        updateData.ApiEndpoint = apiEndpoint;
    }
    if (method) {
        updateData.Method = method;
    }
    await updateSyncLog(syncLogId, updateData);
}
async function markSyncSuccess(syncLogId, duration, message, responsePayload) {
    await updateSyncLog(syncLogId, {
        Status: SyncStatus.SUCCESS,
        Message: message || 'Sync completed successfully',
        Duration: `${duration}ms`,
        ResponsePayload: responsePayload,
        CompletedAt: new Date(),
    });
}
async function markSyncFailed(syncLogId, duration, error, shouldRetry = false) {
    const errorDetails = {
        message: error.message,
        stack: error.stack,
        ...(error.response && {
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data,
        }),
    };
    const updateData = {
        Status: shouldRetry ? SyncStatus.RETRYING : SyncStatus.ERROR,
        Message: `Sync failed: ${error.message}`,
        Duration: `${duration}ms`,
        ErrorDetails: errorDetails,
    };
    if (!shouldRetry) {
        updateData.CompletedAt = new Date();
    }
    if (shouldRetry) {
        updateData.LastRetryAt = new Date();
        updateData.NextRetryAt = new Date(Date.now() + 60000);
    }
    await updateSyncLog(syncLogId, updateData);
}
async function markSyncForRetry(syncLogId, retryCount, nextRetryAt) {
    await updateSyncLog(syncLogId, {
        Status: SyncStatus.RETRYING,
        Message: `Retry attempt ${retryCount}`,
        RetryCount: retryCount,
        LastRetryAt: new Date(),
        NextRetryAt: nextRetryAt,
    });
}
async function getSyncLogs(companyId, entity, limit = 50) {
    try {
        const logs = await getPrismaClient().syncLog.findMany({
            where: {
                CompanyId: companyId,
                ...(entity && { Entity: entity }),
            },
            orderBy: {
                CreatedAt: 'desc',
            },
            take: limit,
        });
        return logs;
    }
    catch (error) {
        console.error('❌ Failed to retrieve sync logs:', {
            error: error.message,
            companyId,
            entity,
        });
        return [];
    }
}
//# sourceMappingURL=syncLogService.js.map