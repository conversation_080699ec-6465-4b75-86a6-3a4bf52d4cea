/**
 * S3 JSON Storage Utility
 *
 * This utility provides functionality to store monthly JSON data to local files
 * and upload them to S3 for backup and archival purposes.
 *
 * Key Features:
 * - Store JSON data to local files with organized directory structure
 * - Upload JSON files to S3 with proper naming conventions
 * - Comprehensive error handling and logging
 * - Support for both tracking and summary data
 * - Automatic file cleanup after successful upload
 *
 * <AUTHOR> Sheet S3 Storage Utility
 * @version 1.0.0
 */

import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { promises as fs } from 'fs';
import path from 'path';
import moment from 'moment';
import { getEnvironmentConfig, getAWSConfig, isOfflineMode } from '../config/environment';
import { ProcessedRowData, ProcessedRowDataWithoutTracking } from '../types';

/**
 * S3 Storage Configuration
 */
const S3_CONFIG = {
    // Local storage paths
    LOCAL_STORAGE_DIR: '/tmp/balance-sheet-json',

    // S3 key prefixes
    S3_PREFIX: 'balance-sheet-data',

    // File naming patterns
    TRACKING_FILE_SUFFIX: 'with-tracking',
    SUMMARY_FILE_SUFFIX: 'summary',

    // JSON formatting options
    JSON_INDENT: 2,
};

/**
 * Interface for monthly JSON data structure
 */
export interface MonthlyBalanceSheetData {
    companyId: string;
    year: number;
    month: number;
    monthKey: string;
    generatedAt: string;
    dataType: 'tracking' | 'summary';
    recordCount: number;
    data: ProcessedRowData[] | ProcessedRowDataWithoutTracking[];
}

/**
 * S3 client configuration
 */
interface S3ClientConfig {
    client: S3Client | null;
    bucketName: string;
}

/**
 * Initialize S3 client configuration
 */
function initializeS3Config(): S3ClientConfig {
    const config = getEnvironmentConfig();
    const bucketName = config.S3_BUCKET_NAME || '';

    // Initialize S3 client only if not in offline mode and bucket is configured
    if (!isOfflineMode() && bucketName) {
        const awsConfig = getAWSConfig();

        // Only initialize S3 client if we have valid credentials
        if (awsConfig.accessKeyId && awsConfig.secretAccessKey) {
            const client = new S3Client({
                region: awsConfig.region,
                credentials: {
                    accessKeyId: awsConfig.accessKeyId,
                    secretAccessKey: awsConfig.secretAccessKey,
                },
            });
            return { client, bucketName };
        } else {
            console.warn('⚠️ S3 credentials not configured, S3 upload will be skipped');
        }
    }

    return { client: null, bucketName };
}

/**
 * Store monthly balance sheet data as JSON files and upload to S3
 *
 * @param companyId - Company identifier
 * @param year - Year of the data
 * @param month - Month of the data
 * @param trackingData - Data with tracking categories
 * @param summaryData - Aggregated data without tracking
 */
async function storeMonthlyData(
    companyId: string,
    year: number,
    month: number,
    trackingData: ProcessedRowData[],
    summaryData: ProcessedRowDataWithoutTracking[]
): Promise<void> {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    console.log(`📁 Storing JSON data for ${monthKey} (Company: ${companyId})`);

    try {
        // Ensure local storage directory exists
        await ensureLocalStorageDirectory();

        // Store tracking data
        if (trackingData && trackingData.length > 0) {
            await storeDataFile(companyId, year, month, trackingData, 'tracking');
        }

        // Store summary data
        if (summaryData && summaryData.length > 0) {
            await storeDataFile(companyId, year, month, summaryData, 'summary');
        }

        console.log(`✅ Successfully stored JSON data for ${monthKey}`);
    } catch (error) {
        console.error(`❌ Failed to store JSON data for ${monthKey}:`, error);
        throw new Error(`JSON storage failed for ${monthKey}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Store individual data file (tracking or summary)
 */
async function storeDataFile(
    companyId: string,
    year: number,
    month: number,
    data: ProcessedRowData[] | ProcessedRowDataWithoutTracking[],
    dataType: 'tracking' | 'summary'
): Promise<void> {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    const s3Config = initializeS3Config();

    // Prepare JSON data structure
    const jsonData: MonthlyBalanceSheetData = {
        companyId,
        year,
        month,
        monthKey,
        generatedAt: moment().toISOString(),
        dataType,
        recordCount: data.length,
        data,
    };

    // Generate file paths
    const fileName = generateFileName(companyId, year, month, dataType);
    const localFilePath = path.join(S3_CONFIG.LOCAL_STORAGE_DIR, fileName);
    const s3Key = generateS3Key(companyId, year, month, dataType);

    try {
        // Write JSON to local file
        await fs.writeFile(
            localFilePath,
            JSON.stringify(jsonData, null, S3_CONFIG.JSON_INDENT),
            'utf8'
        );
        console.log(`📝 Created local JSON file: ${localFilePath}`);

        // Upload to S3 if configured and not in offline mode
        if (s3Config.client && s3Config.bucketName) {
            await uploadToS3(s3Config.client, s3Config.bucketName, localFilePath, s3Key, jsonData);

            // Clean up local file after successful upload
            await cleanupLocalFile(localFilePath);
        } else {
            console.log(`ℹ️ S3 upload skipped (offline mode or bucket not configured): ${fileName}`);
        }
    } catch (error) {
        console.error(`❌ Failed to store ${dataType} data file:`, error);
        throw error;
    }
}

/**
 * Upload JSON file to S3
 */
async function uploadToS3(
    s3Client: S3Client,
    bucketName: string,
    localFilePath: string,
    s3Key: string,
    jsonData: MonthlyBalanceSheetData
): Promise<void> {
    if (!s3Client || !bucketName) {
        throw new Error('S3 client or bucket not configured');
    }

    try {
        const fileContent = await fs.readFile(localFilePath);

        const uploadCommand = new PutObjectCommand({
            Bucket: bucketName,
            Key: s3Key,
            Body: fileContent,
            ContentType: 'application/json',
            Metadata: {
                'company-id': jsonData.companyId,
                'year': jsonData.year.toString(),
                'month': jsonData.month.toString(),
                'data-type': jsonData.dataType,
                'record-count': jsonData.recordCount.toString(),
                'generated-at': jsonData.generatedAt,
            },
        });

        await s3Client.send(uploadCommand);
        console.log(`☁️ Successfully uploaded to S3: ${s3Key}`);
    } catch (error) {
        console.error(`❌ S3 upload failed for ${s3Key}:`, error);
        throw new Error(`S3 upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Generate local file name
 */
function generateFileName(
    companyId: string,
    year: number,
    month: number,
    dataType: 'tracking' | 'summary'
): string {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    const suffix = dataType === 'tracking' ? S3_CONFIG.TRACKING_FILE_SUFFIX : S3_CONFIG.SUMMARY_FILE_SUFFIX;
    return `balance-sheet-${companyId}-${monthKey}-${suffix}.json`;
}

/**
 * Generate S3 key
 */
function generateS3Key(
    companyId: string,
    year: number,
    month: number,
    dataType: 'tracking' | 'summary'
): string {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    const suffix = dataType === 'tracking' ? S3_CONFIG.TRACKING_FILE_SUFFIX : S3_CONFIG.SUMMARY_FILE_SUFFIX;
    return `${S3_CONFIG.S3_PREFIX}/${companyId}/${year}/${monthKey}/balance-sheet-${suffix}.json`;
}

/**
 * Ensure local storage directory exists
 */
async function ensureLocalStorageDirectory(): Promise<void> {
    try {
        await fs.mkdir(S3_CONFIG.LOCAL_STORAGE_DIR, { recursive: true });
    } catch (error) {
        console.error('Failed to create local storage directory:', error);
        throw error;
    }
}

/**
 * Clean up local file after successful upload
 */
async function cleanupLocalFile(filePath: string): Promise<void> {
    try {
        await fs.unlink(filePath);
        console.log(`🗑️ Cleaned up local file: ${filePath}`);
    } catch (error) {
        console.warn(`⚠️ Failed to cleanup local file ${filePath}:`, error);
        // Don't throw error for cleanup failures
    }
}

/**
 * Main export function to store monthly balance sheet data
 */
export async function storeMonthlyBalanceSheetJson(
    companyId: string,
    year: number,
    month: number,
    trackingData: ProcessedRowData[],
    summaryData: ProcessedRowDataWithoutTracking[]
): Promise<void> {
    await storeMonthlyData(companyId, year, month, trackingData, summaryData);
}
