# S3 JSON Storage for Balance Sheet Data

This document describes the S3 JSON storage functionality that has been added to the Balance Sheet synchronization service.

## Overview

The S3 JSON storage utility automatically stores monthly Balance Sheet data as JSON files and uploads them to Amazon S3 for backup and archival purposes. This feature runs alongside the existing database storage and provides an additional layer of data persistence.

## Features

- **Automatic JSON Generation**: Creates structured JSON files for each month's data
- **Dual Data Types**: Stores both tracking data (detailed) and summary data (aggregated)
- **S3 Upload**: Automatically uploads JSON files to configured S3 bucket
- **Organized Storage**: Uses a hierarchical folder structure in S3
- **Error Handling**: Comprehensive error handling with detailed logging
- **Offline Support**: Gracefully handles offline mode and missing configurations
- **File Cleanup**: Automatically removes local files after successful S3 upload

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# AWS Configuration
REGION=us-east-1
ACCESS_KEY_ID=your_aws_access_key_id
SECRET_ACCESS_KEY=your_aws_secret_access_key

# S3 Configuration
S3_BUCKET_NAME=your-balance-sheet-json-bucket
```

### S3 Bucket Setup

1. Create an S3 bucket in your AWS account
2. Configure appropriate IAM permissions for the Lambda function
3. Set the bucket name in the `S3_BUCKET_NAME` environment variable

### Required IAM Permissions

The Lambda function needs the following S3 permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-balance-sheet-json-bucket/*"
        }
    ]
}
```

## File Structure

### Local Storage

JSON files are temporarily stored in `/tmp/balance-sheet-json/` before upload:

```
/tmp/balance-sheet-json/
├── balance-sheet-{companyId}-{YYYY-MM}-with-tracking.json
└── balance-sheet-{companyId}-{YYYY-MM}-summary.json
```

### S3 Storage Structure

Files are organized in S3 with the following hierarchy:

```
s3://your-bucket/
└── balance-sheet-data/
    └── {companyId}/
        └── {year}/
            └── {YYYY-MM}/
                ├── balance-sheet-with-tracking.json
                └── balance-sheet-summary.json
```

### JSON File Format

Each JSON file contains the following structure:

```json
{
    "companyId": "company-uuid",
    "year": 2024,
    "month": 1,
    "monthKey": "2024-01",
    "generatedAt": "2024-01-15T10:30:00.000Z",
    "dataType": "tracking",
    "recordCount": 150,
    "data": [
        {
            "CompanyId": "company-uuid",
            "Year": 2024,
            "Month": 1,
            "AccountId": "account-id",
            "AccountName": "Account Name",
            "Amount": 1000.50,
            "TrackingCategoryId1": "tracking-id-1",
            "TrackingCategoryId2": "tracking-id-2"
        }
    ]
}
```

## Integration

The S3 JSON storage is automatically integrated into the Balance Sheet synchronization process:

1. **Database Storage**: Data is first stored in the database (BalanceSheetTracking and BalanceSheet tables)
2. **JSON Generation**: After successful database storage, JSON files are created
3. **S3 Upload**: JSON files are uploaded to S3 with metadata
4. **Cleanup**: Local files are removed after successful upload

## Error Handling

- **Database Failure**: If database storage fails, JSON storage is skipped
- **JSON Storage Failure**: If JSON storage fails, the database operation is still considered successful
- **S3 Upload Failure**: Logged as warning, doesn't fail the entire sync process
- **Missing Configuration**: Gracefully handles missing S3 configuration

## Testing

A test script is provided to verify the S3 JSON storage functionality:

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run the test
npx ts-node src/test/s3JsonStorageTest.ts
```

## Monitoring and Logging

The service provides comprehensive logging for monitoring:

- **File Creation**: Logs when JSON files are created locally
- **S3 Upload**: Logs successful uploads with S3 keys
- **Error Handling**: Detailed error logging with context
- **Performance**: Logs processing times and file sizes

## Usage Examples

### Manual Usage

```typescript
import { storeMonthlyBalanceSheetJson } from './utils/s3JsonStorage';

// Store monthly data
await storeMonthlyBalanceSheetJson(
    'company-id',
    2024,
    1,
    trackingData,
    summaryData
);
```

### Service Integration

The functionality is automatically integrated into the `storeBalanceSheetData` function in `balanceSheetService.ts`. No additional code changes are required for normal operation.

## Troubleshooting

### Common Issues

1. **S3 Upload Fails**
   - Check AWS credentials
   - Verify S3 bucket exists and is accessible
   - Check IAM permissions

2. **Local File Creation Fails**
   - Check `/tmp` directory permissions
   - Verify disk space availability

3. **JSON Generation Fails**
   - Check data format and structure
   - Verify TypeScript compilation

### Debug Mode

Enable debug logging by setting the log level in your Lambda configuration or local environment.

## Performance Considerations

- **File Size**: JSON files are compressed using standard JSON formatting
- **Memory Usage**: Files are processed in memory before upload
- **Network**: S3 uploads are performed asynchronously
- **Cleanup**: Local files are automatically cleaned up to prevent disk space issues

## Security

- **Credentials**: AWS credentials are managed through environment variables
- **Encryption**: S3 server-side encryption is recommended
- **Access Control**: Use IAM policies to restrict S3 access
- **Data Privacy**: JSON files contain sensitive financial data

## Future Enhancements

Potential improvements for future versions:

- **Compression**: Add gzip compression for JSON files
- **Batch Upload**: Support for batch uploading multiple months
- **Retention Policies**: Automatic cleanup of old JSON files
- **Encryption**: Client-side encryption before upload
- **Notifications**: SNS notifications for successful uploads
