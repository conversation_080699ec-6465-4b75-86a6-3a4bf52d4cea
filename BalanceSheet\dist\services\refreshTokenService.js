"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshXeroToken = refreshXeroToken;
const axiosInstance_1 = __importDefault(require("../utils/axiosInstance"));
const client_1 = require("@prisma/client");
const environment_1 = require("../config/environment");
const types_1 = require("../types");
const prisma = new client_1.PrismaClient();
async function refreshXeroToken(integration) {
    const { tokenUrl, clientId, clientSecret } = (0, environment_1.getXeroConfig)();
    const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");
    const payload = new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: integration.XeroRefreshToken,
    });
    try {
        const res = await axiosInstance_1.default.post(tokenUrl, payload.toString(), {
            headers: {
                Authorization: `Basic ${basicAuth}`,
                "Content-Type": "application/x-www-form-urlencoded",
            },
        });
        const { access_token, refresh_token, expires_in } = res.data;
        const updatedCompany = await prisma.company.update({
            where: { Id: integration.Id },
            data: {
                XeroAccessToken: access_token,
                XeroRefreshToken: refresh_token,
                XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
                XeroRefreshTokenExpiry: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
            },
        });
        return updatedCompany;
    }
    catch (error) {
        const errorMessage = error?.response?.data
            ? JSON.stringify(error.response.data)
            : error instanceof Error
                ? error.message
                : 'Unknown error';
        throw new types_1.TokenRefreshError(`Failed to refresh Xero token: ${errorMessage}`, error?.response?.status || 500);
    }
}
//# sourceMappingURL=refreshTokenService.js.map