"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.storeMonthlyBalanceSheetJson = storeMonthlyBalanceSheetJson;
const client_s3_1 = require("@aws-sdk/client-s3");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const moment_1 = __importDefault(require("moment"));
const environment_1 = require("../config/environment");
const S3_CONFIG = {
    LOCAL_STORAGE_DIR: '/tmp/balance-sheet-json',
    S3_PREFIX: 'balance-sheet-data',
    TRACKING_FILE_SUFFIX: 'with-tracking',
    SUMMARY_FILE_SUFFIX: 'summary',
    JSON_INDENT: 2,
};
function initializeS3Config() {
    const config = (0, environment_1.getEnvironmentConfig)();
    const bucketName = config.S3_BUCKET_NAME || '';
    if (!(0, environment_1.isOfflineMode)() && bucketName) {
        const awsConfig = (0, environment_1.getAWSConfig)();
        if (awsConfig.accessKeyId && awsConfig.secretAccessKey) {
            const client = new client_s3_1.S3Client({
                region: awsConfig.region,
                credentials: {
                    accessKeyId: awsConfig.accessKeyId,
                    secretAccessKey: awsConfig.secretAccessKey,
                },
            });
            return { client, bucketName };
        }
        else {
            console.warn('⚠️ S3 credentials not configured, S3 upload will be skipped');
        }
    }
    return { client: null, bucketName };
}
async function storeMonthlyData(companyId, year, month, trackingData, summaryData) {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    console.log(`📁 Storing JSON data for ${monthKey} (Company: ${companyId})`);
    try {
        await ensureLocalStorageDirectory();
        if (trackingData && trackingData.length > 0) {
            await storeDataFile(companyId, year, month, trackingData, 'tracking');
        }
        if (summaryData && summaryData.length > 0) {
            await storeDataFile(companyId, year, month, summaryData, 'summary');
        }
        console.log(`✅ Successfully stored JSON data for ${monthKey}`);
    }
    catch (error) {
        console.error(`❌ Failed to store JSON data for ${monthKey}:`, error);
        throw new Error(`JSON storage failed for ${monthKey}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function storeDataFile(companyId, year, month, data, dataType) {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    const s3Config = initializeS3Config();
    const jsonData = {
        companyId,
        year,
        month,
        monthKey,
        generatedAt: (0, moment_1.default)().toISOString(),
        dataType,
        recordCount: data.length,
        data,
    };
    const fileName = generateFileName(companyId, year, month, dataType);
    const localFilePath = path_1.default.join(S3_CONFIG.LOCAL_STORAGE_DIR, fileName);
    const s3Key = generateS3Key(companyId, year, month, dataType);
    try {
        await fs_1.promises.writeFile(localFilePath, JSON.stringify(jsonData, null, S3_CONFIG.JSON_INDENT), 'utf8');
        console.log(`📝 Created local JSON file: ${localFilePath}`);
        if (s3Config.client && s3Config.bucketName) {
            await uploadToS3(s3Config.client, s3Config.bucketName, localFilePath, s3Key, jsonData);
            await cleanupLocalFile(localFilePath);
        }
        else {
            console.log(`ℹ️ S3 upload skipped (offline mode or bucket not configured): ${fileName}`);
        }
    }
    catch (error) {
        console.error(`❌ Failed to store ${dataType} data file:`, error);
        throw error;
    }
}
async function uploadToS3(s3Client, bucketName, localFilePath, s3Key, jsonData) {
    if (!s3Client || !bucketName) {
        throw new Error('S3 client or bucket not configured');
    }
    try {
        const fileContent = await fs_1.promises.readFile(localFilePath);
        const uploadCommand = new client_s3_1.PutObjectCommand({
            Bucket: bucketName,
            Key: s3Key,
            Body: fileContent,
            ContentType: 'application/json',
            Metadata: {
                'company-id': jsonData.companyId,
                'year': jsonData.year.toString(),
                'month': jsonData.month.toString(),
                'data-type': jsonData.dataType,
                'record-count': jsonData.recordCount.toString(),
                'generated-at': jsonData.generatedAt,
            },
        });
        await s3Client.send(uploadCommand);
        console.log(`☁️ Successfully uploaded to S3: ${s3Key}`);
    }
    catch (error) {
        console.error(`❌ S3 upload failed for ${s3Key}:`, error);
        throw new Error(`S3 upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
function generateFileName(companyId, year, month, dataType) {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    const suffix = dataType === 'tracking' ? S3_CONFIG.TRACKING_FILE_SUFFIX : S3_CONFIG.SUMMARY_FILE_SUFFIX;
    return `balance-sheet-${companyId}-${monthKey}-${suffix}.json`;
}
function generateS3Key(companyId, year, month, dataType) {
    const monthKey = `${year}-${String(month).padStart(2, '0')}`;
    const suffix = dataType === 'tracking' ? S3_CONFIG.TRACKING_FILE_SUFFIX : S3_CONFIG.SUMMARY_FILE_SUFFIX;
    return `${S3_CONFIG.S3_PREFIX}/${companyId}/${year}/${monthKey}/balance-sheet-${suffix}.json`;
}
async function ensureLocalStorageDirectory() {
    try {
        await fs_1.promises.mkdir(S3_CONFIG.LOCAL_STORAGE_DIR, { recursive: true });
    }
    catch (error) {
        console.error('Failed to create local storage directory:', error);
        throw error;
    }
}
async function cleanupLocalFile(filePath) {
    try {
        await fs_1.promises.unlink(filePath);
        console.log(`🗑️ Cleaned up local file: ${filePath}`);
    }
    catch (error) {
        console.warn(`⚠️ Failed to cleanup local file ${filePath}:`, error);
    }
}
async function storeMonthlyBalanceSheetJson(companyId, year, month, trackingData, summaryData) {
    await storeMonthlyData(companyId, year, month, trackingData, summaryData);
}
//# sourceMappingURL=s3JsonStorage.js.map