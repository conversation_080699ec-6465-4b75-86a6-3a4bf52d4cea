"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3JsonStorageService = void 0;
exports.getS3JsonStorageService = getS3JsonStorageService;
exports.storeMonthlyBalanceSheetJson = storeMonthlyBalanceSheetJson;
const client_s3_1 = require("@aws-sdk/client-s3");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const moment_1 = __importDefault(require("moment"));
const environment_1 = require("../config/environment");
const S3_CONFIG = {
    LOCAL_STORAGE_DIR: '/tmp/balance-sheet-json',
    S3_PREFIX: 'balance-sheet-data',
    TRACKING_FILE_SUFFIX: 'with-tracking',
    SUMMARY_FILE_SUFFIX: 'summary',
    JSON_INDENT: 2,
};
class S3JsonStorageService {
    constructor() {
        this.s3Client = null;
        const config = (0, environment_1.getEnvironmentConfig)();
        this.bucketName = config.S3_BUCKET_NAME || '';
        if (!(0, environment_1.isOfflineMode)() && this.bucketName) {
            const awsConfig = (0, environment_1.getAWSConfig)();
            if (awsConfig.accessKeyId && awsConfig.secretAccessKey) {
                this.s3Client = new client_s3_1.S3Client({
                    region: awsConfig.region,
                    credentials: {
                        accessKeyId: awsConfig.accessKeyId,
                        secretAccessKey: awsConfig.secretAccessKey,
                    },
                });
            }
            else {
                console.warn('⚠️ S3 credentials not configured, S3 upload will be skipped');
            }
        }
    }
    async storeMonthlyData(companyId, year, month, trackingData, summaryData) {
        const monthKey = `${year}-${String(month).padStart(2, '0')}`;
        console.log(`📁 Storing JSON data for ${monthKey} (Company: ${companyId})`);
        try {
            await this.ensureLocalStorageDirectory();
            if (trackingData && trackingData.length > 0) {
                await this.storeDataFile(companyId, year, month, trackingData, 'tracking');
            }
            if (summaryData && summaryData.length > 0) {
                await this.storeDataFile(companyId, year, month, summaryData, 'summary');
            }
            console.log(`✅ Successfully stored JSON data for ${monthKey}`);
        }
        catch (error) {
            console.error(`❌ Failed to store JSON data for ${monthKey}:`, error);
            throw new Error(`JSON storage failed for ${monthKey}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async storeDataFile(companyId, year, month, data, dataType) {
        const monthKey = `${year}-${String(month).padStart(2, '0')}`;
        const jsonData = {
            companyId,
            year,
            month,
            monthKey,
            generatedAt: (0, moment_1.default)().toISOString(),
            dataType,
            recordCount: data.length,
            data,
        };
        const fileName = this.generateFileName(companyId, year, month, dataType);
        const localFilePath = path_1.default.join(S3_CONFIG.LOCAL_STORAGE_DIR, fileName);
        const s3Key = this.generateS3Key(companyId, year, month, dataType);
        try {
            await fs_1.promises.writeFile(localFilePath, JSON.stringify(jsonData, null, S3_CONFIG.JSON_INDENT), 'utf8');
            console.log(`📝 Created local JSON file: ${localFilePath}`);
            if (this.s3Client && this.bucketName) {
                await this.uploadToS3(localFilePath, s3Key, jsonData);
                await this.cleanupLocalFile(localFilePath);
            }
            else {
                console.log(`ℹ️ S3 upload skipped (offline mode or bucket not configured): ${fileName}`);
            }
        }
        catch (error) {
            console.error(`❌ Failed to store ${dataType} data file:`, error);
            throw error;
        }
    }
    async uploadToS3(localFilePath, s3Key, jsonData) {
        if (!this.s3Client || !this.bucketName) {
            throw new Error('S3 client or bucket not configured');
        }
        try {
            const fileContent = await fs_1.promises.readFile(localFilePath);
            const uploadCommand = new client_s3_1.PutObjectCommand({
                Bucket: this.bucketName,
                Key: s3Key,
                Body: fileContent,
                ContentType: 'application/json',
                Metadata: {
                    'company-id': jsonData.companyId,
                    'year': jsonData.year.toString(),
                    'month': jsonData.month.toString(),
                    'data-type': jsonData.dataType,
                    'record-count': jsonData.recordCount.toString(),
                    'generated-at': jsonData.generatedAt,
                },
            });
            await this.s3Client.send(uploadCommand);
            console.log(`☁️ Successfully uploaded to S3: ${s3Key}`);
        }
        catch (error) {
            console.error(`❌ S3 upload failed for ${s3Key}:`, error);
            throw new Error(`S3 upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    generateFileName(companyId, year, month, dataType) {
        const monthKey = `${year}-${String(month).padStart(2, '0')}`;
        const suffix = dataType === 'tracking' ? S3_CONFIG.TRACKING_FILE_SUFFIX : S3_CONFIG.SUMMARY_FILE_SUFFIX;
        return `balance-sheet-${companyId}-${monthKey}-${suffix}.json`;
    }
    generateS3Key(companyId, year, month, dataType) {
        const monthKey = `${year}-${String(month).padStart(2, '0')}`;
        const suffix = dataType === 'tracking' ? S3_CONFIG.TRACKING_FILE_SUFFIX : S3_CONFIG.SUMMARY_FILE_SUFFIX;
        return `${S3_CONFIG.S3_PREFIX}/${companyId}/${year}/${monthKey}/balance-sheet-${suffix}.json`;
    }
    async ensureLocalStorageDirectory() {
        try {
            await fs_1.promises.mkdir(S3_CONFIG.LOCAL_STORAGE_DIR, { recursive: true });
        }
        catch (error) {
            console.error('Failed to create local storage directory:', error);
            throw error;
        }
    }
    async cleanupLocalFile(filePath) {
        try {
            await fs_1.promises.unlink(filePath);
            console.log(`🗑️ Cleaned up local file: ${filePath}`);
        }
        catch (error) {
            console.warn(`⚠️ Failed to cleanup local file ${filePath}:`, error);
        }
    }
}
exports.S3JsonStorageService = S3JsonStorageService;
let s3JsonStorageInstance = null;
function getS3JsonStorageService() {
    if (!s3JsonStorageInstance) {
        s3JsonStorageInstance = new S3JsonStorageService();
    }
    return s3JsonStorageInstance;
}
async function storeMonthlyBalanceSheetJson(companyId, year, month, trackingData, summaryData) {
    const storageService = getS3JsonStorageService();
    await storageService.storeMonthlyData(companyId, year, month, trackingData, summaryData);
}
//# sourceMappingURL=s3JsonStorage.js.map