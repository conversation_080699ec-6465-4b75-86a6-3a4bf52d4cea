import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { LambdaClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../LambdaClient";
import { FunctionEventInvokeConfig, GetFunctionEventInvokeConfigRequest } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetFunctionEventInvokeConfigCommand}.
 */
export interface GetFunctionEventInvokeConfigCommandInput extends GetFunctionEventInvokeConfigRequest {
}
/**
 * @public
 *
 * The output of {@link GetFunctionEventInvokeConfigCommand}.
 */
export interface GetFunctionEventInvokeConfigCommandOutput extends FunctionEventInvokeConfig, __MetadataBearer {
}
declare const GetFunctionEventInvokeConfigCommand_base: {
    new (input: GetFunctionEventInvokeConfigCommandInput): import("@smithy/smithy-client").CommandImpl<GetFunctionEventInvokeConfigCommandInput, GetFunctionEventInvokeConfigCommandOutput, LambdaClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: GetFunctionEventInvokeConfigCommandInput): import("@smithy/smithy-client").CommandImpl<GetFunctionEventInvokeConfigCommandInput, GetFunctionEventInvokeConfigCommandOutput, LambdaClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Retrieves the configuration for asynchronous invocation for a function, version, or alias.</p>
 *          <p>To configure options for asynchronous invocation, use <a>PutFunctionEventInvokeConfig</a>.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { LambdaClient, GetFunctionEventInvokeConfigCommand } from "@aws-sdk/client-lambda"; // ES Modules import
 * // const { LambdaClient, GetFunctionEventInvokeConfigCommand } = require("@aws-sdk/client-lambda"); // CommonJS import
 * const client = new LambdaClient(config);
 * const input = { // GetFunctionEventInvokeConfigRequest
 *   FunctionName: "STRING_VALUE", // required
 *   Qualifier: "STRING_VALUE",
 * };
 * const command = new GetFunctionEventInvokeConfigCommand(input);
 * const response = await client.send(command);
 * // { // FunctionEventInvokeConfig
 * //   LastModified: new Date("TIMESTAMP"),
 * //   FunctionArn: "STRING_VALUE",
 * //   MaximumRetryAttempts: Number("int"),
 * //   MaximumEventAgeInSeconds: Number("int"),
 * //   DestinationConfig: { // DestinationConfig
 * //     OnSuccess: { // OnSuccess
 * //       Destination: "STRING_VALUE",
 * //     },
 * //     OnFailure: { // OnFailure
 * //       Destination: "STRING_VALUE",
 * //     },
 * //   },
 * // };
 *
 * ```
 *
 * @param GetFunctionEventInvokeConfigCommandInput - {@link GetFunctionEventInvokeConfigCommandInput}
 * @returns {@link GetFunctionEventInvokeConfigCommandOutput}
 * @see {@link GetFunctionEventInvokeConfigCommandInput} for command's `input` shape.
 * @see {@link GetFunctionEventInvokeConfigCommandOutput} for command's `response` shape.
 * @see {@link LambdaClientResolvedConfig | config} for LambdaClient's `config` shape.
 *
 * @throws {@link InvalidParameterValueException} (client fault)
 *  <p>One of the parameters in the request is not valid.</p>
 *
 * @throws {@link ResourceNotFoundException} (client fault)
 *  <p>The resource specified in the request does not exist.</p>
 *
 * @throws {@link ServiceException} (server fault)
 *  <p>The Lambda service encountered an internal error.</p>
 *
 * @throws {@link TooManyRequestsException} (client fault)
 *  <p>The request throughput limit was exceeded. For more information, see <a href="https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html#api-requests">Lambda quotas</a>.</p>
 *
 * @throws {@link LambdaServiceException}
 * <p>Base exception class for all service exceptions from Lambda service.</p>
 *
 *
 * @example To get an asynchronous invocation configuration
 * ```javascript
 * // The following example returns the asynchronous invocation configuration for the BLUE alias of a function named my-function.
 * const input = {
 *   FunctionName: "my-function",
 *   Qualifier: "BLUE"
 * };
 * const command = new GetFunctionEventInvokeConfigCommand(input);
 * const response = await client.send(command);
 * /* response is
 * {
 *   DestinationConfig: {
 *     OnFailure: {
 *       Destination: "arn:aws:sqs:us-east-2:123456789012:failed-invocations"
 *     },
 *     OnSuccess:     { /* empty *\/ }
 *   },
 *   FunctionArn: "arn:aws:lambda:us-east-2:123456789012:function:my-function:BLUE",
 *   LastModified: "2016-11-21T19:49:20.006Z",
 *   MaximumEventAgeInSeconds: 3600,
 *   MaximumRetryAttempts: 0
 * }
 * *\/
 * ```
 *
 * @public
 */
export declare class GetFunctionEventInvokeConfigCommand extends GetFunctionEventInvokeConfigCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetFunctionEventInvokeConfigRequest;
            output: FunctionEventInvokeConfig;
        };
        sdk: {
            input: GetFunctionEventInvokeConfigCommandInput;
            output: GetFunctionEventInvokeConfigCommandOutput;
        };
    };
}
