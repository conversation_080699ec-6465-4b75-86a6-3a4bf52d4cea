export declare const RequestChecksumCalculation: {
  readonly WHEN_SUPPORTED: "WHEN_SUPPORTED";
  readonly WHEN_REQUIRED: "WHEN_REQUIRED";
};
export type RequestChecksumCalculation =
  (typeof RequestChecksumCalculation)[keyof typeof RequestChecksumCalculation];
export declare const DEFAULT_REQUEST_CHECKSUM_CALCULATION: "WHEN_SUPPORTED";
export declare const ResponseChecksumValidation: {
  readonly WHEN_SUPPORTED: "WHEN_SUPPORTED";
  readonly WHEN_REQUIRED: "WHEN_REQUIRED";
};
export type ResponseChecksumValidation =
  (typeof ResponseChecksumValidation)[keyof typeof ResponseChecksumValidation];
export declare const DEFAULT_RESPONSE_CHECKSUM_VALIDATION: "WHEN_SUPPORTED";
export declare enum ChecksumAlgorithm {
  MD5 = "MD5",
  CRC32 = "CRC32",
  CRC32C = "CRC32C",
  CRC64NVME = "CRC64NVME",
  SHA1 = "SHA1",
  SHA256 = "SHA256",
}
export declare enum ChecksumLocation {
  HEADER = "header",
  TRAILER = "trailer",
}
export declare const DEFAULT_CHECKSUM_ALGORITHM = ChecksumAlgorithm.CRC32;
