import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetObjectRetentionOutput,
  GetObjectRetentionRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetObjectRetentionCommandInput
  extends GetObjectRetentionRequest {}
export interface GetObjectRetentionCommandOutput
  extends GetObjectRetentionOutput,
    __MetadataBearer {}
declare const GetObjectRetentionCommand_base: {
  new (
    input: GetObjectRetentionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetObjectRetentionCommandInput,
    GetObjectRetentionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetObjectRetentionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetObjectRetentionCommandInput,
    GetObjectRetentionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetObjectRetentionCommand extends GetObjectRetentionCommand_base {
  protected static __types: {
    api: {
      input: GetObjectRetentionRequest;
      output: GetObjectRetentionOutput;
    };
    sdk: {
      input: GetObjectRetentionCommandInput;
      output: GetObjectRetentionCommandOutput;
    };
  };
}
