import { ProcessedRowDataWithoutTracking } from '../types';
export declare function extractMonthlyBalanceSheetData(report: any, trackingCategories: any[], requestData: any, month: {
    year: number;
    month: number;
}): {
    Year: number;
    Month: number;
    AccountId: any;
    AccountName: any;
    Amount: number;
    TrackingCategoryId1: any;
    TrackingCategoryId2: any;
    CompanyId: any;
}[];
export declare function extractMonthlyBalanceSheetDataWithoutTracking(report: any, requestData: any, month: {
    year: number;
    month: number;
}): ProcessedRowDataWithoutTracking[];
//# sourceMappingURL=extractMonthlyBalanceSheetData.d.ts.map