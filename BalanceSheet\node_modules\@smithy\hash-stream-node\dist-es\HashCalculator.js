import { toUint8Array } from "@smithy/util-utf8";
import { Writable } from "stream";
export class HashCalculator extends Writable {
    constructor(hash, options) {
        super(options);
        this.hash = hash;
    }
    _write(chunk, encoding, callback) {
        try {
            this.hash.update(toUint8Array(chunk));
        }
        catch (err) {
            return callback(err);
        }
        callback();
    }
}
