"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationError = exports.TokenRefreshError = exports.XeroError = void 0;
class XeroError extends Error {
    constructor(message, statusCode, originalError) {
        super(message);
        this.statusCode = statusCode;
        this.originalError = originalError;
        this.name = 'XeroError';
    }
}
exports.XeroError = XeroError;
class TokenRefreshError extends XeroError {
    constructor(message, originalError) {
        super(message, 401, originalError);
        this.name = 'TokenRefreshError';
    }
}
exports.TokenRefreshError = TokenRefreshError;
class ValidationError extends Error {
    constructor(message, missingFields) {
        super(message);
        this.missingFields = missingFields;
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
//# sourceMappingURL=index.js.map