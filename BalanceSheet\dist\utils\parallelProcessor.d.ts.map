{"version": 3, "file": "parallelProcessor.d.ts", "sourceRoot": "", "sources": ["../../src/utils/parallelProcessor.ts"], "names": [], "mappings": "AAwBA,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,UAAU,CAAC;AAE7D,MAAM,WAAW,yBAAyB;IACtC,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,gBAAgB,CAAC,CAAC;IAC/B,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,CAAC,CAAC;IACT,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,WAAW,CAAC,CAAC;IAC1B,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/B,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,KAAK,CAAC;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,KAAK,CAAA;KAAE,CAAC,CAAC;CAClD;AAKD,wBAAgB,mBAAmB,CAC/B,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,GAChB,KAAK,CAAC;IAAE,SAAS,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC,CAwC9D;AAQD,wBAAsB,uBAAuB,CAAC,CAAC,EAC3C,SAAS,EAAE,CAAC,WAAW,EAAE,eAAe,KAAK,OAAO,CAAC,CAAC,CAAC,EACvD,eAAe,EAAE,eAAe,EAChC,OAAO,GAAE,yBAA8B,GACxC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAiFzB;AAwGD,wBAAgB,mBAAmB,CAC/B,OAAO,EAAE,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,EAAE,GAChD,gBAAgB,EAAE,CAmBpB;AAQD,wBAAsB,oBAAoB,CAAC,CAAC,EACxC,SAAS,EAAE,CAAC,WAAW,EAAE,eAAe,KAAK,OAAO,CAAC,CAAC,CAAC,EACvD,eAAe,EAAE,eAAe,EAChC,OAAO,GAAE,yBAA8B,GACxC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAkEzB"}