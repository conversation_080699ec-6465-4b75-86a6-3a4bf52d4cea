export declare enum SyncStatus {
    PENDING = "PENDING",
    IN_PROGRESS = "IN_PROGRESS",
    SUCCESS = "SUCCESS",
    WARNING = "WARNING",
    ERROR = "ERROR",
    RETRYING = "RETRYING",
    CANCELLED = "CANCELLED"
}
export interface SyncLogData {
    requestId?: string;
    entity: string;
    integration: string;
    apiEndpoint?: string;
    method?: string;
    companyId: string;
    userId?: string;
    requestPayload?: any;
    maxRetries?: number;
}
export interface SyncLogUpdate {
    Status?: SyncStatus;
    Message?: string;
    Duration?: string;
    ResponsePayload?: any;
    ErrorDetails?: any;
    RetryCount?: number;
    LastRetryAt?: Date;
    NextRetryAt?: Date;
    CompletedAt?: Date;
    ApiEndpoint?: string;
    Method?: string;
}
export declare function createSyncLog(logData: SyncLogData): Promise<string>;
export declare function updateSyncLog(syncLogId: string, updateData: SyncLogUpdate): Promise<void>;
export declare function markSyncStarted(syncLogId: string, apiEndpoint?: string, method?: string): Promise<void>;
export declare function markSyncSuccess(syncLogId: string, duration: number, message?: string, responsePayload?: any): Promise<void>;
export declare function markSyncFailed(syncLogId: string, duration: number, error: any, shouldRetry?: boolean): Promise<void>;
export declare function markSyncForRetry(syncLogId: string, retryCount: number, nextRetryAt: Date): Promise<void>;
export declare function getSyncLogs(companyId: string, entity?: string, limit?: number): Promise<{
    Id: string;
    UserId: string | null;
    CreatedAt: Date;
    UpdatedAt: Date;
    RequestId: string | null;
    Entity: string;
    Integration: string;
    ApiEndpoint: string | null;
    Method: string | null;
    Status: import(".prisma/client").$Enums.SyncStatus;
    Message: string | null;
    Duration: string | null;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    StartedAt: Date;
    CompletedAt: Date | null;
    CompanyId: string;
    RequestPayload: import("@prisma/client/runtime/library").JsonValue | null;
    ResponsePayload: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
}[]>;
//# sourceMappingURL=syncLogService.d.ts.map