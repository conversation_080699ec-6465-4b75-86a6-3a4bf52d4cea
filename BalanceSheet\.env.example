# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Xero API Configuration
XERO_CLIENT_ID=your_xero_client_id_here
XERO_CLIENT_SECRET=your_xero_client_secret_here
XERO_TOKEN_URL=https://identity.xero.com/connect/token
XERO_BASE_URL=https://api.xero.com/api.xro/2.0/

# Retry Configuration
FIRST_RETRY=1000
SECOND_RETRY=2000
LAST_RETRY=5000

# AWS Configuration
REGION=us-east-1
ACCESS_KEY_ID=your_aws_access_key_id
SECRET_ACCESS_KEY=your_aws_secret_access_key

# S3 Configuration for JSON Storage
S3_BUCKET_NAME=your-balance-sheet-json-bucket

# Environment
IS_OFFLINE=false

# Prisma Configuration (for Lambda deployment)
PRISMA_QUERY_ENGINE_LIBRARY=/var/task/node_modules/.prisma/client/libquery_engine-rhel-openssl-1.0.x.so.node
