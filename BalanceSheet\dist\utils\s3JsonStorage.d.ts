import { ProcessedRowData, ProcessedRowDataWithoutTracking } from '../types';
export interface MonthlyBalanceSheetData {
    companyId: string;
    year: number;
    month: number;
    monthKey: string;
    generatedAt: string;
    dataType: 'tracking' | 'summary';
    recordCount: number;
    data: ProcessedRowData[] | ProcessedRowDataWithoutTracking[];
}
export declare function storeMonthlyBalanceSheetJson(companyId: string, year: number, month: number, trackingData: ProcessedRowData[], summaryData: ProcessedRowDataWithoutTracking[]): Promise<void>;
//# sourceMappingURL=s3JsonStorage.d.ts.map