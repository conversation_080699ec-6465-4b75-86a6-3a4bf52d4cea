import { ProcessedRowData, ProcessedRowDataWithoutTracking } from '../types';
export interface MonthlyBalanceSheetData {
    companyId: string;
    year: number;
    month: number;
    monthKey: string;
    generatedAt: string;
    dataType: 'tracking' | 'summary';
    recordCount: number;
    data: ProcessedRowData[] | ProcessedRowDataWithoutTracking[];
}
export declare class S3JsonStorageService {
    private s3Client;
    private bucketName;
    constructor();
    storeMonthlyData(companyId: string, year: number, month: number, trackingData: ProcessedRowData[], summaryData: ProcessedRowDataWithoutTracking[]): Promise<void>;
    private storeDataFile;
    private uploadToS3;
    private generateFileName;
    private generateS3Key;
    private ensureLocalStorageDirectory;
    private cleanupLocalFile;
}
export declare function getS3JsonStorageService(): S3JsonStorageService;
export declare function storeMonthlyBalanceSheetJson(companyId: string, year: number, month: number, trackingData: ProcessedRowData[], summaryData: ProcessedRowDataWithoutTracking[]): Promise<void>;
//# sourceMappingURL=s3JsonStorage.d.ts.map