import { RelativeMiddlewareOptions, SerializeMiddleware } from "@smithy/types";
import { PreviouslyResolved } from "./configuration";
export interface FlexibleChecksumsInputMiddlewareConfig {
  requestValidationModeMember?: string;
}
export declare const flexibleChecksumsInputMiddlewareOptions: RelativeMiddlewareOptions;
export declare const flexibleChecksumsInputMiddleware: (
  config: PreviouslyResolved,
  middlewareConfig: FlexibleChecksumsInputMiddlewareConfig
) => SerializeMiddleware<any, any>;
