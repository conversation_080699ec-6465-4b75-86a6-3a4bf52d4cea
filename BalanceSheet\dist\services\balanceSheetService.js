"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processBalanceSheetRequest = processBalanceSheetRequest;
exports.performHealthCheck = performHealthCheck;
exports.getSyncStatistics = getSyncStatistics;
const moment_1 = __importDefault(require("moment"));
const client_1 = require("@prisma/client");
const types_1 = require("../types");
const refreshTokenService_1 = require("./refreshTokenService");
const getXeroTrackingCategories_1 = require("./getXeroTrackingCategories");
const environment_1 = require("../config/environment");
const axiosInstance_1 = __importDefault(require("../utils/axiosInstance"));
const extractMonthlyBalanceSheetData_1 = require("../services/extractMonthlyBalanceSheetData");
const p_limit_1 = __importDefault(require("p-limit"));
const syncLogService_1 = require("./syncLogService");
const s3JsonStorage_1 = require("../utils/s3JsonStorage");
const apiLogService_1 = require("./apiLogService");
const PRODUCTION_CONFIG = {
    XERO_API_DELAY_MS: 1000,
    API_TIMEOUT_MS: 30000,
    INITIAL_SYNC_MONTHS: 60,
    REGULAR_SYNC_MONTHS: 13,
    INITIAL_SYNC_CONCURRENCY: 1,
    REGULAR_SYNC_CONCURRENCY: 2,
    TOKEN_REFRESH_BUFFER_MS: 10 * 60 * 1000,
    BATCH_SIZE: 1000,
    TRANSACTION_TIMEOUT_MS: 60000,
    PERFORMANCE_LOG_THRESHOLD_MS: 5000,
    ERROR_RETRY_ATTEMPTS: 3,
    MAX_MONTHS_PER_REQUEST: 120,
    MIN_YEAR: 2000,
    MAX_YEAR: new Date().getFullYear() + 1,
};
let prisma = null;
function getPrismaClient() {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'],
            errorFormat: 'pretty',
            transactionOptions: {
                maxWait: 10000,
                timeout: 60000,
            },
        };
        if (process.env.IS_OFFLINE === 'true') {
            delete process.env.PRISMA_QUERY_ENGINE_LIBRARY;
        }
        prisma = new client_1.PrismaClient(config);
    }
    return prisma;
}
async function processBalanceSheetRequest(requestData, _context) {
    const startTime = Date.now();
    let syncLogId = null;
    try {
        console.log(`🔄 Starting Balance Sheet sync for company: ${requestData.companyId}`);
        const syncLogData = {
            entity: 'BalanceSheet',
            integration: 'Xero',
            companyId: requestData.companyId,
            requestPayload: {
                companyId: requestData.companyId,
            },
            maxRetries: 3,
        };
        syncLogId = await (0, syncLogService_1.createSyncLog)(syncLogData);
        console.log(`🚀 Starting Balance Sheet sync - Sync Log ID: ${syncLogId}`);
        validateRequestData(requestData);
        const integration = await getActiveIntegration(requestData);
        console.log(`✅ Found active integration for company: ${requestData.companyId}`);
        const validIntegration = await ensureValidToken(integration);
        console.log(`✅ Token validated for company: ${requestData.companyId}`);
        const syncPeriod = await determineSyncPeriod(requestData.companyId, validIntegration.FinancialYearEnd);
        console.log(`📅 Sync period determined: ${syncPeriod.monthsToSync} months (${syncPeriod.startDate} to ${syncPeriod.endDate})`);
        const monthRanges = generateMonthRanges(syncPeriod.startDate, syncPeriod.endDate);
        console.log(`📊 Generated ${monthRanges.length} month ranges for processing`);
        const concurrency = syncPeriod.isInitialSync
            ? PRODUCTION_CONFIG.INITIAL_SYNC_CONCURRENCY
            : PRODUCTION_CONFIG.REGULAR_SYNC_CONCURRENCY;
        console.log(`⚙️ Using concurrency level: ${concurrency} (${syncPeriod.isInitialSync ? 'initial' : 'regular'} sync)`);
        const limit = (0, p_limit_1.default)(concurrency);
        const processingStartTime = Date.now();
        if (syncLogId) {
            await (0, syncLogService_1.markSyncStarted)(syncLogId, 'Reports/BalanceSheet', 'GET');
        }
        const tasks = monthRanges.map((monthData) => limit(() => processMonthBalanceSheet(validIntegration, requestData, monthData)));
        const results = await Promise.allSettled(tasks);
        const processingTime = Date.now() - processingStartTime;
        let processed = 0;
        let errors = 0;
        const errorDetails = [];
        for (const [index, result] of results.entries()) {
            if (result.status === 'fulfilled') {
                processed++;
            }
            else {
                errors++;
                errorDetails.push(`Month ${monthRanges[index].startDate}: ${result.reason?.message || 'Unknown error'}`);
            }
        }
        console.log(`🎉 Balance Sheet sync completed for company ${requestData.companyId}:`);
        console.log(`   📈 Processed: ${processed}/${monthRanges.length} months`);
        console.log(`   ❌ Errors: ${errors} months`);
        console.log(`   ⏱️ Total time: ${processingTime}ms`);
        console.log(`   🔄 Sync type: ${syncPeriod.isInitialSync ? 'Initial (5 years)' : 'Regular (13 months)'}`);
        if (errors > 0) {
            console.warn(`⚠️ Some months failed to process:`, errorDetails);
        }
        if (processed === 0) {
            throw new Error('No months were successfully processed');
        }
        if (syncLogId) {
            const duration = Date.now() - startTime;
            await (0, syncLogService_1.markSyncSuccess)(syncLogId, duration, `Balance Sheet sync completed successfully: ${processed} months processed, ${errors} errors`, {
                monthsProcessed: processed,
                totalErrors: errors,
                syncType: syncPeriod.isInitialSync ? 'Initial (5 years)' : 'Regular (13 months)',
                duration: `${duration}ms`,
                processingTime: `${processingTime}ms`
            });
        }
        console.log(`✅ Balance Sheet sync completed successfully in ${Date.now() - startTime}ms`);
    }
    catch (error) {
        if (syncLogId) {
            const duration = Date.now() - startTime;
            await (0, syncLogService_1.markSyncFailed)(syncLogId, duration, error, false);
        }
        console.error(`❌ Balance Sheet sync failed for company ${requestData.companyId} after ${Date.now() - startTime}ms:`, {
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}
function validateRequestData(data) {
    const missingFields = [];
    const invalidFields = [];
    if (!data?.companyId) {
        missingFields.push('companyId');
    }
    else if (typeof data.companyId !== 'string' || data.companyId.trim().length === 0) {
        invalidFields.push('companyId (must be non-empty string)');
    }
    const allErrors = [...missingFields, ...invalidFields];
    if (allErrors.length > 0) {
        const errorMessage = [
            missingFields.length > 0 ? `Missing fields: ${missingFields.join(', ')}` : '',
            invalidFields.length > 0 ? `Invalid fields: ${invalidFields.join(', ')}` : ''
        ].filter(Boolean).join('; ');
        throw new types_1.ValidationError(errorMessage, allErrors);
    }
}
async function determineSyncPeriod(companyId, financialYearEnd) {
    console.log(`🔍 Determining sync period for company: ${companyId}`);
    try {
        const existingData = await getPrismaClient().balanceSheetTracking.findFirst({
            where: { CompanyId: companyId },
            orderBy: { Year: 'desc' },
            select: { Year: true, Month: true }
        });
        const now = (0, moment_1.default)();
        const fyEnd = financialYearEnd ? (0, moment_1.default)(financialYearEnd) : now.clone().endOf('month');
        if (!existingData) {
            const startDate = fyEnd.clone().subtract(PRODUCTION_CONFIG.INITIAL_SYNC_MONTHS, 'months').startOf('month');
            console.log(`📅 Initial sync detected - fetching ${PRODUCTION_CONFIG.INITIAL_SYNC_MONTHS} months of data from ${startDate.format('YYYY-MM-DD')}`);
            return {
                startDate: startDate.format('YYYY-MM-DD'),
                endDate: fyEnd.format('YYYY-MM-DD'),
                monthsToSync: PRODUCTION_CONFIG.INITIAL_SYNC_MONTHS,
                isInitialSync: true
            };
        }
        else {
            const startDate = fyEnd.clone().subtract(PRODUCTION_CONFIG.REGULAR_SYNC_MONTHS, 'months').startOf('month');
            console.log(`📅 Regular sync detected - fetching ${PRODUCTION_CONFIG.REGULAR_SYNC_MONTHS} months from ${startDate.format('YYYY-MM-DD')} (last data: ${existingData.Year}-${existingData.Month})`);
            return {
                startDate: startDate.format('YYYY-MM-DD'),
                endDate: fyEnd.format('YYYY-MM-DD'),
                monthsToSync: PRODUCTION_CONFIG.REGULAR_SYNC_MONTHS,
                isInitialSync: false
            };
        }
    }
    catch (error) {
        console.error(`❌ Error determining sync period for company ${companyId}:`, error.message);
        const now = (0, moment_1.default)();
        const fyEnd = financialYearEnd ? (0, moment_1.default)(financialYearEnd) : now.clone().endOf('month');
        const startDate = fyEnd.clone().subtract(PRODUCTION_CONFIG.REGULAR_SYNC_MONTHS, 'months').startOf('month');
        return {
            startDate: startDate.format('YYYY-MM-DD'),
            endDate: fyEnd.format('YYYY-MM-DD'),
            monthsToSync: PRODUCTION_CONFIG.REGULAR_SYNC_MONTHS,
            isInitialSync: false
        };
    }
}
function generateMonthRanges(startDate, endDate) {
    const start = (0, moment_1.default)(startDate);
    const end = (0, moment_1.default)(endDate);
    const ranges = [];
    if (!start.isValid() || !end.isValid()) {
        throw new Error(`Invalid date range: ${startDate} to ${endDate}`);
    }
    if (start.isAfter(end)) {
        throw new Error(`Start date ${startDate} cannot be after end date ${endDate}`);
    }
    let current = start.clone().startOf('month');
    console.log(`📅 Generating month ranges from ${current.format('YYYY-MM-DD')} to ${end.format('YYYY-MM-DD')}`);
    while (current.isSameOrBefore(end, 'month')) {
        const monthEnd = current.clone().endOf('month');
        if (monthEnd.isAfter(end)) {
            monthEnd.set({
                date: end.date(),
                hour: end.hour(),
                minute: end.minute(),
                second: end.second()
            });
        }
        ranges.push({
            startDate: current.format('YYYY-MM-DD'),
            endDate: monthEnd.format('YYYY-MM-DD'),
            monthDate: current.clone(),
            year: current.year(),
            month: current.month() + 1,
        });
        current.add(1, 'month');
    }
    console.log(`📊 Generated ${ranges.length} month ranges for processing`);
    return ranges;
}
async function getActiveIntegration(data) {
    console.log(`🔍 Fetching active integration for company: ${data.companyId}`);
    try {
        const prisma = getPrismaClient();
        const integration = await prisma.company.findFirst({
            where: {
                Id: data.companyId,
                ConnectionStatus: 'ACTIVE',
            },
            select: {
                Id: true,
                XeroAccessToken: true,
                XeroRefreshToken: true,
                XeroTokenExpiry: true,
                XeroTenantId: true,
                FinancialYearEnd: true,
                ConnectionStatus: true,
            }
        });
        if (!integration) {
            throw new types_1.XeroError(`Xero integration not found or inactive for company: ${data.companyId}`, 404);
        }
        if (!integration.XeroAccessToken || !integration.XeroTenantId) {
            throw new types_1.XeroError(`Incomplete Xero integration for company: ${data.companyId}`, 400);
        }
        console.log(`✅ Found active integration for company: ${data.companyId}`);
        return integration;
    }
    catch (error) {
        console.error(`❌ Error fetching integration for company ${data.companyId}:`, error.message);
        throw error;
    }
}
async function ensureValidToken(integration) {
    const tokenExpiry = new Date(integration.XeroTokenExpiry);
    const now = new Date();
    const timeUntilExpiry = tokenExpiry.getTime() - now.getTime();
    console.log(`🔐 Checking token validity for company: ${integration.Id}`);
    console.log(`   Token expires: ${tokenExpiry.toISOString()}`);
    console.log(`   Time until expiry: ${Math.round(timeUntilExpiry / 1000 / 60)} minutes`);
    if (timeUntilExpiry <= PRODUCTION_CONFIG.TOKEN_REFRESH_BUFFER_MS) {
        console.log(`🔄 Token expires soon, refreshing for company: ${integration.Id}`);
        try {
            const refreshedIntegration = await (0, refreshTokenService_1.refreshXeroToken)(integration);
            console.log(`✅ Token refreshed successfully for company: ${integration.Id}`);
            return refreshedIntegration;
        }
        catch (error) {
            console.error(`❌ Token refresh failed for company ${integration.Id}:`, error.message);
            throw new Error(`Failed to refresh Xero token: ${error.message}`);
        }
    }
    console.log(`✅ Token is valid for company: ${integration.Id}`);
    return integration;
}
const getBalanceSheet = async (accessToken, tenantId, requestData) => {
    const startTime = Date.now();
    console.log(`🔍 Fetching Balance Sheet WITH tracking categories for date: ${requestData.endDate}`);
    const { baseUrl } = (0, environment_1.getXeroConfig)();
    let url = `${baseUrl}Reports/BalanceSheet?date=${requestData.endDate}`;
    try {
        let trackingCategories = [];
        try {
            trackingCategories = await (0, getXeroTrackingCategories_1.getXeroTrackingCategories)(accessToken, tenantId);
            console.log(`📊 Found ${trackingCategories.length} tracking categories`);
        }
        catch (trackingError) {
            console.warn(`⚠️ Failed to fetch tracking categories: ${trackingError.message}`);
        }
        let params = '';
        if (trackingCategories.length > 0) {
            const trackingParams = [];
            trackingCategories.forEach((category, index) => {
                if (index === 0) {
                    trackingParams.push(`trackingCategoryID=${category.categoryId}`);
                }
                else {
                    trackingParams.push(`trackingCategoryID${index + 1}=${category.categoryId}`);
                }
            });
            params = trackingParams.join('&');
        }
        url = `${baseUrl}Reports/BalanceSheet?date=${requestData.endDate}${params ? '&' + params : ''}`;
        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);
        const response = await axiosInstance_1.default.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'BalanceSheetSync/2.0.0',
            },
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS,
        });
        const requestTime = Date.now() - startTime;
        console.log(`✅ Balance Sheet API call completed in ${requestTime}ms`);
        const reportData = response?.data;
        if (!reportData) {
            throw new Error('Empty response received from Xero API');
        }
        if (!reportData.Reports || !Array.isArray(reportData.Reports) || reportData.Reports.length === 0) {
            throw new Error('No reports found in Xero API response');
        }
        if (!reportData.Reports[0]) {
            throw new Error('Invalid Balance Sheet report structure received from Xero');
        }
        const dateObj = new Date(requestData.endDate);
        const month = {
            year: dateObj.getFullYear(),
            month: dateObj.getMonth() + 1
        };
        console.log(`🔄 Processing Balance Sheet data for ${month.year}-${month.month}`);
        const processedData = (0, extractMonthlyBalanceSheetData_1.extractMonthlyBalanceSheetData)(reportData.Reports[0], trackingCategories, requestData, month);
        console.log(`✅ Processed ${processedData.length} Balance Sheet rows with tracking categories`);
        await (0, apiLogService_1.logSuccessfulApiCall)(requestData.companyId, 'GET', url.replace(accessToken, '[REDACTED]'), requestTime, 'BalanceSheet', {
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...',
            withTracking: true
        }, { recordCount: processedData.length });
        return processedData;
    }
    catch (error) {
        const requestTime = Date.now() - startTime;
        console.error(`❌ Balance Sheet API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...'
        });
        await (0, apiLogService_1.logFailedApiCall)(requestData.companyId, 'GET', url.replace(accessToken, '[REDACTED]'), requestTime, 'BalanceSheet', error, {
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...',
            withTracking: true
        });
        if (error.response?.status === 429) {
            const retryAfter = error.response.headers['retry-after'] || 60;
            throw new Error(`Xero API rate limit exceeded. Retry after ${retryAfter} seconds. Consider reducing concurrency.`);
        }
        if (error.response?.status === 401) {
            throw new Error('Xero API authentication failed. Token may be expired or invalid.');
        }
        if (error.response?.status === 403) {
            throw new Error('Xero API access forbidden. Check tenant permissions.');
        }
        if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
            throw new Error(`Network error connecting to Xero API: ${error.message}`);
        }
        throw new Error(`Balance Sheet API call failed: ${error.message}`);
    }
};
const getBalanceSheetWithoutTracking = async (accessToken, tenantId, requestData) => {
    const startTime = Date.now();
    console.log(`🔍 Fetching Balance Sheet WITHOUT tracking categories for date: ${requestData.endDate}`);
    const { baseUrl } = (0, environment_1.getXeroConfig)();
    const url = `${baseUrl}Reports/BalanceSheet?date=${requestData.endDate}`;
    try {
        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);
        const response = await axiosInstance_1.default.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'BalanceSheetSync/2.0.0',
            },
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS,
        });
        const requestTime = Date.now() - startTime;
        console.log(`✅ Balance Sheet API call (without tracking) completed in ${requestTime}ms`);
        const reportData = response?.data;
        if (!reportData) {
            throw new Error('Empty response received from Xero API');
        }
        if (!reportData.Reports || !Array.isArray(reportData.Reports) || reportData.Reports.length === 0) {
            throw new Error('No reports found in Xero API response');
        }
        if (!reportData.Reports[0]) {
            throw new Error('Invalid Balance Sheet report structure received from Xero');
        }
        const dateObj = new Date(requestData.endDate);
        const month = {
            year: dateObj.getFullYear(),
            month: dateObj.getMonth() + 1
        };
        console.log(`🔄 Processing aggregated Balance Sheet data for ${month.year}-${month.month}`);
        const processedData = (0, extractMonthlyBalanceSheetData_1.extractMonthlyBalanceSheetDataWithoutTracking)(reportData.Reports[0], requestData, month);
        console.log(`✅ Processed ${processedData.length} aggregated Balance Sheet rows`);
        await (0, apiLogService_1.logSuccessfulApiCall)(requestData.companyId, 'GET', url.replace(accessToken, '[REDACTED]'), requestTime, 'BalanceSheet', {
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...',
            withTracking: false
        }, { recordCount: processedData.length });
        return processedData;
    }
    catch (error) {
        const requestTime = Date.now() - startTime;
        console.error(`❌ Balance Sheet API call (without tracking) failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...'
        });
        await (0, apiLogService_1.logFailedApiCall)(requestData.companyId, 'GET', url.replace(accessToken, '[REDACTED]'), requestTime, 'BalanceSheet', error, {
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...',
            withTracking: false
        });
        if (error.response?.status === 429) {
            const retryAfter = error.response.headers['retry-after'] || 60;
            throw new Error(`Xero API rate limit exceeded. Retry after ${retryAfter} seconds. Consider reducing concurrency.`);
        }
        if (error.response?.status === 401) {
            throw new Error('Xero API authentication failed. Token may be expired or invalid.');
        }
        if (error.response?.status === 403) {
            throw new Error('Xero API access forbidden. Check tenant permissions.');
        }
        if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
            throw new Error(`Network error connecting to Xero API: ${error.message}`);
        }
        throw new Error(`Balance Sheet API call (without tracking) failed: ${error.message}`);
    }
};
async function processMonthBalanceSheet(integration, requestData, month) {
    const monthKey = `${month.year}-${String(month.month).padStart(2, '0')}`;
    const startTime = Date.now();
    try {
        console.log(`📊 Processing Balance Sheet for ${monthKey} (${month.endDate})`);
        if (!integration.XeroAccessToken || !integration.XeroTenantId) {
            throw new Error(`Missing Xero credentials for company: ${integration.Id}`);
        }
        const monthlyRequestData = {
            ...requestData,
            startDate: month.startDate,
            endDate: month.endDate,
        };
        console.log(`🔍 [1/2] Fetching Balance Sheet WITH tracking categories for ${monthKey}`);
        const balanceSheetDataWithTracking = await getBalanceSheet(integration.XeroAccessToken, integration.XeroTenantId, monthlyRequestData);
        console.log(`⏱️ Rate limiting delay (${PRODUCTION_CONFIG.XERO_API_DELAY_MS}ms) before second API call...`);
        await new Promise(resolve => setTimeout(resolve, PRODUCTION_CONFIG.XERO_API_DELAY_MS));
        console.log(`🔍 [2/2] Fetching Balance Sheet WITHOUT tracking categories for ${monthKey}`);
        const balanceSheetDataWithoutTracking = await getBalanceSheetWithoutTracking(integration.XeroAccessToken, integration.XeroTenantId, monthlyRequestData);
        if (requestData.dumpToDatabase) {
            await storeBalanceSheetData(requestData.companyId, month, balanceSheetDataWithTracking, balanceSheetDataWithoutTracking, monthKey);
        }
        else {
            console.log(`ℹ️ Database storage skipped for ${monthKey} (dumpToDatabase=false)`);
        }
        const processingTime = Date.now() - startTime;
        console.log(`🎉 Successfully processed Balance Sheet for ${monthKey} in ${processingTime}ms`);
    }
    catch (error) {
        const processingTime = Date.now() - startTime;
        console.error(`❌ Failed to process Balance Sheet for ${monthKey} after ${processingTime}ms:`, {
            error: error.message,
            companyId: requestData.companyId,
            month: monthKey,
            stack: error.stack?.split('\n').slice(0, 3).join('\n')
        });
        throw error;
    }
}
async function storeBalanceSheetData(companyId, month, trackingData, summaryData, monthKey) {
    const prisma = getPrismaClient();
    const maxRetries = 3;
    const baseDelay = 1000;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`💾 Storing Balance Sheet data for ${monthKey} (attempt ${attempt}/${maxRetries})...`);
            await prisma.$transaction(async (tx) => {
                if (trackingData && trackingData.length > 0) {
                    console.log(`🗑️ Clearing existing tracking data for ${monthKey}...`);
                    await tx.balanceSheetTracking.deleteMany({
                        where: {
                            CompanyId: companyId,
                            Year: month.year,
                            Month: month.month,
                        },
                    });
                    const batchSize = 100;
                    const batches = Math.ceil(trackingData.length / batchSize);
                    for (let i = 0; i < batches; i++) {
                        const start = i * batchSize;
                        const end = Math.min(start + batchSize, trackingData.length);
                        const batch = trackingData.slice(start, end);
                        console.log(`📝 Inserting tracking batch ${i + 1}/${batches} (${batch.length} rows) for ${monthKey}...`);
                        await tx.balanceSheetTracking.createMany({
                            data: batch,
                            skipDuplicates: true,
                        });
                    }
                    console.log(`✅ Stored ${trackingData.length} Balance Sheet tracking rows for ${monthKey}`);
                }
                else {
                    console.log(`ℹ️ No tracking data to store for ${monthKey}`);
                }
                if (summaryData && summaryData.length > 0) {
                    console.log(`🗑️ Clearing existing summary data for ${monthKey}...`);
                    await tx.balanceSheet.deleteMany({
                        where: {
                            CompanyId: companyId,
                            Year: month.year,
                            Month: month.month,
                        },
                    });
                    const batchSize = 100;
                    const batches = Math.ceil(summaryData.length / batchSize);
                    for (let i = 0; i < batches; i++) {
                        const start = i * batchSize;
                        const end = Math.min(start + batchSize, summaryData.length);
                        const batch = summaryData.slice(start, end);
                        console.log(`📝 Inserting summary batch ${i + 1}/${batches} (${batch.length} rows) for ${monthKey}...`);
                        await tx.balanceSheet.createMany({
                            data: batch,
                            skipDuplicates: true,
                        });
                    }
                    console.log(`✅ Stored ${summaryData.length} Balance Sheet summary rows for ${monthKey}`);
                }
                else {
                    console.log(`ℹ️ No summary data to store for ${monthKey}`);
                }
            }, {
                timeout: 60000,
                maxWait: 10000,
            });
            console.log(`💾 Database transaction completed successfully for ${monthKey}`);
            try {
                console.log(`📁 Storing JSON files for ${monthKey}...`);
                await (0, s3JsonStorage_1.storeMonthlyBalanceSheetJson)(companyId, month.year, month.month, trackingData, summaryData);
                console.log(`✅ JSON storage completed successfully for ${monthKey}`);
            }
            catch (jsonError) {
                console.error(`⚠️ JSON storage failed for ${monthKey} (database storage was successful):`, {
                    error: jsonError.message,
                    companyId,
                    monthKey
                });
            }
            return;
        }
        catch (error) {
            const isLastAttempt = attempt === maxRetries;
            const isTransactionError = error.message?.includes('Transaction') ||
                error.message?.includes('timeout') ||
                error.message?.includes('Unable to start a transaction');
            console.error(`❌ Database storage failed for ${monthKey} (attempt ${attempt}/${maxRetries}):`, {
                error: error.message,
                companyId,
                trackingRows: trackingData?.length || 0,
                summaryRows: summaryData?.length || 0,
                isTransactionError,
                willRetry: !isLastAttempt && isTransactionError
            });
            if (isLastAttempt || !isTransactionError) {
                throw new Error(`Failed to store Balance Sheet data for ${monthKey}: ${error.message}`);
            }
            const delay = baseDelay * Math.pow(2, attempt - 1);
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
async function performHealthCheck(companyId) {
    const checks = {};
    try {
        const start = Date.now();
        const prisma = getPrismaClient();
        await prisma.$queryRaw `SELECT 1`;
        checks.database = {
            status: 'pass',
            message: 'Database connection successful',
            duration: Date.now() - start
        };
    }
    catch (error) {
        checks.database = {
            status: 'fail',
            message: `Database connection failed: ${error.message}`
        };
    }
    try {
        const start = Date.now();
        await getActiveIntegration({ companyId, userId: 'health-check' });
        checks.integration = {
            status: 'pass',
            message: `Active integration found for company: ${companyId}`,
            duration: Date.now() - start
        };
    }
    catch (error) {
        checks.integration = {
            status: 'fail',
            message: `Integration check failed: ${error.message}`
        };
    }
    try {
        const config = (0, environment_1.getXeroConfig)();
        if (!config.baseUrl || !config.clientId) {
            throw new Error('Missing required Xero configuration');
        }
        checks.configuration = {
            status: 'pass',
            message: 'Xero configuration valid'
        };
    }
    catch (error) {
        checks.configuration = {
            status: 'fail',
            message: `Configuration check failed: ${error.message}`
        };
    }
    const healthy = Object.values(checks).every(check => check.status === 'pass');
    return { healthy, checks };
}
async function getSyncStatistics(companyId) {
    const prisma = getPrismaClient();
    try {
        const latestTracking = await prisma.balanceSheetTracking.findFirst({
            where: { CompanyId: companyId },
            orderBy: [{ Year: 'desc' }, { Month: 'desc' }],
            select: { Year: true, Month: true }
        });
        await prisma.balanceSheet.findFirst({
            where: { CompanyId: companyId },
            orderBy: [{ Year: 'desc' }, { Month: 'desc' }],
            select: { Year: true, Month: true }
        });
        const trackingCount = await prisma.balanceSheetTracking.groupBy({
            by: ['Year', 'Month'],
            where: { CompanyId: companyId },
            _count: true
        });
        const summaryCount = await prisma.balanceSheet.groupBy({
            by: ['Year', 'Month'],
            where: { CompanyId: companyId },
            _count: true
        });
        const trackingMonths = new Set(trackingCount.map(t => `${t.Year}-${t.Month}`));
        const summaryMonths = new Set(summaryCount.map(s => `${s.Year}-${s.Month}`));
        const missingInSummary = [...trackingMonths].filter(month => !summaryMonths.has(month));
        const missingInTracking = [...summaryMonths].filter(month => !trackingMonths.has(month));
        const missingMonths = [...missingInSummary, ...missingInTracking];
        return {
            lastSyncDate: latestTracking ? `${latestTracking.Year}-${String(latestTracking.Month).padStart(2, '0')}` : null,
            totalMonthsTracking: trackingCount.length,
            totalMonthsSummary: summaryCount.length,
            dataIntegrity: {
                trackingVsSummaryMatch: missingMonths.length === 0,
                missingMonths
            },
            performance: {
                avgProcessingTime: null,
                lastProcessingTime: null
            }
        };
    }
    catch (error) {
        console.error(`❌ Error getting sync statistics for company ${companyId}:`, error.message);
        throw error;
    }
}
//# sourceMappingURL=balanceSheetService.js.map