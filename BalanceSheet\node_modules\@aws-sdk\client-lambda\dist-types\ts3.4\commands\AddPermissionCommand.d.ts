import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  AddPermissionRequest,
  AddPermissionResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface AddPermissionCommandInput extends AddPermissionRequest {}
export interface AddPermissionCommandOutput
  extends AddPermissionResponse,
    __MetadataBearer {}
declare const AddPermissionCommand_base: {
  new (
    input: AddPermissionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AddPermissionCommandInput,
    AddPermissionCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AddPermissionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AddPermissionCommandInput,
    AddPermissionCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AddPermissionCommand extends AddPermissionCommand_base {
  protected static __types: {
    api: {
      input: AddPermissionRequest;
      output: AddPermissionResponse;
    };
    sdk: {
      input: AddPermissionCommandInput;
      output: AddPermissionCommandOutput;
    };
  };
}
