import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { EventStreamSerdeContext as __EventStreamSerdeContext, SdkStreamSerdeContext as __SdkStreamSerdeContext, SerdeContext as __SerdeContext } from "@smithy/types";
import { AbortMultipartUploadCommandInput, AbortMultipartUploadCommandOutput } from "../commands/AbortMultipartUploadCommand";
import { CompleteMultipartUploadCommandInput, CompleteMultipartUploadCommandOutput } from "../commands/CompleteMultipartUploadCommand";
import { CopyObjectCommandInput, CopyObjectCommandOutput } from "../commands/CopyObjectCommand";
import { CreateBucketCommandInput, CreateBucketCommandOutput } from "../commands/CreateBucketCommand";
import { CreateBucketMetadataTableConfigurationCommandInput, CreateBucketMetadataTableConfigurationCommandOutput } from "../commands/CreateBucketMetadataTableConfigurationCommand";
import { CreateMultipartUploadCommandInput, CreateMultipartUploadCommandOutput } from "../commands/CreateMultipartUploadCommand";
import { CreateSessionCommandInput, CreateSessionCommandOutput } from "../commands/CreateSessionCommand";
import { DeleteBucketAnalyticsConfigurationCommandInput, DeleteBucketAnalyticsConfigurationCommandOutput } from "../commands/DeleteBucketAnalyticsConfigurationCommand";
import { DeleteBucketCommandInput, DeleteBucketCommandOutput } from "../commands/DeleteBucketCommand";
import { DeleteBucketCorsCommandInput, DeleteBucketCorsCommandOutput } from "../commands/DeleteBucketCorsCommand";
import { DeleteBucketEncryptionCommandInput, DeleteBucketEncryptionCommandOutput } from "../commands/DeleteBucketEncryptionCommand";
import { DeleteBucketIntelligentTieringConfigurationCommandInput, DeleteBucketIntelligentTieringConfigurationCommandOutput } from "../commands/DeleteBucketIntelligentTieringConfigurationCommand";
import { DeleteBucketInventoryConfigurationCommandInput, DeleteBucketInventoryConfigurationCommandOutput } from "../commands/DeleteBucketInventoryConfigurationCommand";
import { DeleteBucketLifecycleCommandInput, DeleteBucketLifecycleCommandOutput } from "../commands/DeleteBucketLifecycleCommand";
import { DeleteBucketMetadataTableConfigurationCommandInput, DeleteBucketMetadataTableConfigurationCommandOutput } from "../commands/DeleteBucketMetadataTableConfigurationCommand";
import { DeleteBucketMetricsConfigurationCommandInput, DeleteBucketMetricsConfigurationCommandOutput } from "../commands/DeleteBucketMetricsConfigurationCommand";
import { DeleteBucketOwnershipControlsCommandInput, DeleteBucketOwnershipControlsCommandOutput } from "../commands/DeleteBucketOwnershipControlsCommand";
import { DeleteBucketPolicyCommandInput, DeleteBucketPolicyCommandOutput } from "../commands/DeleteBucketPolicyCommand";
import { DeleteBucketReplicationCommandInput, DeleteBucketReplicationCommandOutput } from "../commands/DeleteBucketReplicationCommand";
import { DeleteBucketTaggingCommandInput, DeleteBucketTaggingCommandOutput } from "../commands/DeleteBucketTaggingCommand";
import { DeleteBucketWebsiteCommandInput, DeleteBucketWebsiteCommandOutput } from "../commands/DeleteBucketWebsiteCommand";
import { DeleteObjectCommandInput, DeleteObjectCommandOutput } from "../commands/DeleteObjectCommand";
import { DeleteObjectsCommandInput, DeleteObjectsCommandOutput } from "../commands/DeleteObjectsCommand";
import { DeleteObjectTaggingCommandInput, DeleteObjectTaggingCommandOutput } from "../commands/DeleteObjectTaggingCommand";
import { DeletePublicAccessBlockCommandInput, DeletePublicAccessBlockCommandOutput } from "../commands/DeletePublicAccessBlockCommand";
import { GetBucketAccelerateConfigurationCommandInput, GetBucketAccelerateConfigurationCommandOutput } from "../commands/GetBucketAccelerateConfigurationCommand";
import { GetBucketAclCommandInput, GetBucketAclCommandOutput } from "../commands/GetBucketAclCommand";
import { GetBucketAnalyticsConfigurationCommandInput, GetBucketAnalyticsConfigurationCommandOutput } from "../commands/GetBucketAnalyticsConfigurationCommand";
import { GetBucketCorsCommandInput, GetBucketCorsCommandOutput } from "../commands/GetBucketCorsCommand";
import { GetBucketEncryptionCommandInput, GetBucketEncryptionCommandOutput } from "../commands/GetBucketEncryptionCommand";
import { GetBucketIntelligentTieringConfigurationCommandInput, GetBucketIntelligentTieringConfigurationCommandOutput } from "../commands/GetBucketIntelligentTieringConfigurationCommand";
import { GetBucketInventoryConfigurationCommandInput, GetBucketInventoryConfigurationCommandOutput } from "../commands/GetBucketInventoryConfigurationCommand";
import { GetBucketLifecycleConfigurationCommandInput, GetBucketLifecycleConfigurationCommandOutput } from "../commands/GetBucketLifecycleConfigurationCommand";
import { GetBucketLocationCommandInput, GetBucketLocationCommandOutput } from "../commands/GetBucketLocationCommand";
import { GetBucketLoggingCommandInput, GetBucketLoggingCommandOutput } from "../commands/GetBucketLoggingCommand";
import { GetBucketMetadataTableConfigurationCommandInput, GetBucketMetadataTableConfigurationCommandOutput } from "../commands/GetBucketMetadataTableConfigurationCommand";
import { GetBucketMetricsConfigurationCommandInput, GetBucketMetricsConfigurationCommandOutput } from "../commands/GetBucketMetricsConfigurationCommand";
import { GetBucketNotificationConfigurationCommandInput, GetBucketNotificationConfigurationCommandOutput } from "../commands/GetBucketNotificationConfigurationCommand";
import { GetBucketOwnershipControlsCommandInput, GetBucketOwnershipControlsCommandOutput } from "../commands/GetBucketOwnershipControlsCommand";
import { GetBucketPolicyCommandInput, GetBucketPolicyCommandOutput } from "../commands/GetBucketPolicyCommand";
import { GetBucketPolicyStatusCommandInput, GetBucketPolicyStatusCommandOutput } from "../commands/GetBucketPolicyStatusCommand";
import { GetBucketReplicationCommandInput, GetBucketReplicationCommandOutput } from "../commands/GetBucketReplicationCommand";
import { GetBucketRequestPaymentCommandInput, GetBucketRequestPaymentCommandOutput } from "../commands/GetBucketRequestPaymentCommand";
import { GetBucketTaggingCommandInput, GetBucketTaggingCommandOutput } from "../commands/GetBucketTaggingCommand";
import { GetBucketVersioningCommandInput, GetBucketVersioningCommandOutput } from "../commands/GetBucketVersioningCommand";
import { GetBucketWebsiteCommandInput, GetBucketWebsiteCommandOutput } from "../commands/GetBucketWebsiteCommand";
import { GetObjectAclCommandInput, GetObjectAclCommandOutput } from "../commands/GetObjectAclCommand";
import { GetObjectAttributesCommandInput, GetObjectAttributesCommandOutput } from "../commands/GetObjectAttributesCommand";
import { GetObjectCommandInput, GetObjectCommandOutput } from "../commands/GetObjectCommand";
import { GetObjectLegalHoldCommandInput, GetObjectLegalHoldCommandOutput } from "../commands/GetObjectLegalHoldCommand";
import { GetObjectLockConfigurationCommandInput, GetObjectLockConfigurationCommandOutput } from "../commands/GetObjectLockConfigurationCommand";
import { GetObjectRetentionCommandInput, GetObjectRetentionCommandOutput } from "../commands/GetObjectRetentionCommand";
import { GetObjectTaggingCommandInput, GetObjectTaggingCommandOutput } from "../commands/GetObjectTaggingCommand";
import { GetObjectTorrentCommandInput, GetObjectTorrentCommandOutput } from "../commands/GetObjectTorrentCommand";
import { GetPublicAccessBlockCommandInput, GetPublicAccessBlockCommandOutput } from "../commands/GetPublicAccessBlockCommand";
import { HeadBucketCommandInput, HeadBucketCommandOutput } from "../commands/HeadBucketCommand";
import { HeadObjectCommandInput, HeadObjectCommandOutput } from "../commands/HeadObjectCommand";
import { ListBucketAnalyticsConfigurationsCommandInput, ListBucketAnalyticsConfigurationsCommandOutput } from "../commands/ListBucketAnalyticsConfigurationsCommand";
import { ListBucketIntelligentTieringConfigurationsCommandInput, ListBucketIntelligentTieringConfigurationsCommandOutput } from "../commands/ListBucketIntelligentTieringConfigurationsCommand";
import { ListBucketInventoryConfigurationsCommandInput, ListBucketInventoryConfigurationsCommandOutput } from "../commands/ListBucketInventoryConfigurationsCommand";
import { ListBucketMetricsConfigurationsCommandInput, ListBucketMetricsConfigurationsCommandOutput } from "../commands/ListBucketMetricsConfigurationsCommand";
import { ListBucketsCommandInput, ListBucketsCommandOutput } from "../commands/ListBucketsCommand";
import { ListDirectoryBucketsCommandInput, ListDirectoryBucketsCommandOutput } from "../commands/ListDirectoryBucketsCommand";
import { ListMultipartUploadsCommandInput, ListMultipartUploadsCommandOutput } from "../commands/ListMultipartUploadsCommand";
import { ListObjectsCommandInput, ListObjectsCommandOutput } from "../commands/ListObjectsCommand";
import { ListObjectsV2CommandInput, ListObjectsV2CommandOutput } from "../commands/ListObjectsV2Command";
import { ListObjectVersionsCommandInput, ListObjectVersionsCommandOutput } from "../commands/ListObjectVersionsCommand";
import { ListPartsCommandInput, ListPartsCommandOutput } from "../commands/ListPartsCommand";
import { PutBucketAccelerateConfigurationCommandInput, PutBucketAccelerateConfigurationCommandOutput } from "../commands/PutBucketAccelerateConfigurationCommand";
import { PutBucketAclCommandInput, PutBucketAclCommandOutput } from "../commands/PutBucketAclCommand";
import { PutBucketAnalyticsConfigurationCommandInput, PutBucketAnalyticsConfigurationCommandOutput } from "../commands/PutBucketAnalyticsConfigurationCommand";
import { PutBucketCorsCommandInput, PutBucketCorsCommandOutput } from "../commands/PutBucketCorsCommand";
import { PutBucketEncryptionCommandInput, PutBucketEncryptionCommandOutput } from "../commands/PutBucketEncryptionCommand";
import { PutBucketIntelligentTieringConfigurationCommandInput, PutBucketIntelligentTieringConfigurationCommandOutput } from "../commands/PutBucketIntelligentTieringConfigurationCommand";
import { PutBucketInventoryConfigurationCommandInput, PutBucketInventoryConfigurationCommandOutput } from "../commands/PutBucketInventoryConfigurationCommand";
import { PutBucketLifecycleConfigurationCommandInput, PutBucketLifecycleConfigurationCommandOutput } from "../commands/PutBucketLifecycleConfigurationCommand";
import { PutBucketLoggingCommandInput, PutBucketLoggingCommandOutput } from "../commands/PutBucketLoggingCommand";
import { PutBucketMetricsConfigurationCommandInput, PutBucketMetricsConfigurationCommandOutput } from "../commands/PutBucketMetricsConfigurationCommand";
import { PutBucketNotificationConfigurationCommandInput, PutBucketNotificationConfigurationCommandOutput } from "../commands/PutBucketNotificationConfigurationCommand";
import { PutBucketOwnershipControlsCommandInput, PutBucketOwnershipControlsCommandOutput } from "../commands/PutBucketOwnershipControlsCommand";
import { PutBucketPolicyCommandInput, PutBucketPolicyCommandOutput } from "../commands/PutBucketPolicyCommand";
import { PutBucketReplicationCommandInput, PutBucketReplicationCommandOutput } from "../commands/PutBucketReplicationCommand";
import { PutBucketRequestPaymentCommandInput, PutBucketRequestPaymentCommandOutput } from "../commands/PutBucketRequestPaymentCommand";
import { PutBucketTaggingCommandInput, PutBucketTaggingCommandOutput } from "../commands/PutBucketTaggingCommand";
import { PutBucketVersioningCommandInput, PutBucketVersioningCommandOutput } from "../commands/PutBucketVersioningCommand";
import { PutBucketWebsiteCommandInput, PutBucketWebsiteCommandOutput } from "../commands/PutBucketWebsiteCommand";
import { PutObjectAclCommandInput, PutObjectAclCommandOutput } from "../commands/PutObjectAclCommand";
import { PutObjectCommandInput, PutObjectCommandOutput } from "../commands/PutObjectCommand";
import { PutObjectLegalHoldCommandInput, PutObjectLegalHoldCommandOutput } from "../commands/PutObjectLegalHoldCommand";
import { PutObjectLockConfigurationCommandInput, PutObjectLockConfigurationCommandOutput } from "../commands/PutObjectLockConfigurationCommand";
import { PutObjectRetentionCommandInput, PutObjectRetentionCommandOutput } from "../commands/PutObjectRetentionCommand";
import { PutObjectTaggingCommandInput, PutObjectTaggingCommandOutput } from "../commands/PutObjectTaggingCommand";
import { PutPublicAccessBlockCommandInput, PutPublicAccessBlockCommandOutput } from "../commands/PutPublicAccessBlockCommand";
import { RenameObjectCommandInput, RenameObjectCommandOutput } from "../commands/RenameObjectCommand";
import { RestoreObjectCommandInput, RestoreObjectCommandOutput } from "../commands/RestoreObjectCommand";
import { SelectObjectContentCommandInput, SelectObjectContentCommandOutput } from "../commands/SelectObjectContentCommand";
import { UploadPartCommandInput, UploadPartCommandOutput } from "../commands/UploadPartCommand";
import { UploadPartCopyCommandInput, UploadPartCopyCommandOutput } from "../commands/UploadPartCopyCommand";
import { WriteGetObjectResponseCommandInput, WriteGetObjectResponseCommandOutput } from "../commands/WriteGetObjectResponseCommand";
/**
 * serializeAws_restXmlAbortMultipartUploadCommand
 */
export declare const se_AbortMultipartUploadCommand: (input: AbortMultipartUploadCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCompleteMultipartUploadCommand
 */
export declare const se_CompleteMultipartUploadCommand: (input: CompleteMultipartUploadCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCopyObjectCommand
 */
export declare const se_CopyObjectCommand: (input: CopyObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateBucketCommand
 */
export declare const se_CreateBucketCommand: (input: CreateBucketCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateBucketMetadataTableConfigurationCommand
 */
export declare const se_CreateBucketMetadataTableConfigurationCommand: (input: CreateBucketMetadataTableConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateMultipartUploadCommand
 */
export declare const se_CreateMultipartUploadCommand: (input: CreateMultipartUploadCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateSessionCommand
 */
export declare const se_CreateSessionCommand: (input: CreateSessionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketCommand
 */
export declare const se_DeleteBucketCommand: (input: DeleteBucketCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketAnalyticsConfigurationCommand
 */
export declare const se_DeleteBucketAnalyticsConfigurationCommand: (input: DeleteBucketAnalyticsConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketCorsCommand
 */
export declare const se_DeleteBucketCorsCommand: (input: DeleteBucketCorsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketEncryptionCommand
 */
export declare const se_DeleteBucketEncryptionCommand: (input: DeleteBucketEncryptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketIntelligentTieringConfigurationCommand
 */
export declare const se_DeleteBucketIntelligentTieringConfigurationCommand: (input: DeleteBucketIntelligentTieringConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketInventoryConfigurationCommand
 */
export declare const se_DeleteBucketInventoryConfigurationCommand: (input: DeleteBucketInventoryConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketLifecycleCommand
 */
export declare const se_DeleteBucketLifecycleCommand: (input: DeleteBucketLifecycleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketMetadataTableConfigurationCommand
 */
export declare const se_DeleteBucketMetadataTableConfigurationCommand: (input: DeleteBucketMetadataTableConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketMetricsConfigurationCommand
 */
export declare const se_DeleteBucketMetricsConfigurationCommand: (input: DeleteBucketMetricsConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketOwnershipControlsCommand
 */
export declare const se_DeleteBucketOwnershipControlsCommand: (input: DeleteBucketOwnershipControlsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketPolicyCommand
 */
export declare const se_DeleteBucketPolicyCommand: (input: DeleteBucketPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketReplicationCommand
 */
export declare const se_DeleteBucketReplicationCommand: (input: DeleteBucketReplicationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketTaggingCommand
 */
export declare const se_DeleteBucketTaggingCommand: (input: DeleteBucketTaggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteBucketWebsiteCommand
 */
export declare const se_DeleteBucketWebsiteCommand: (input: DeleteBucketWebsiteCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteObjectCommand
 */
export declare const se_DeleteObjectCommand: (input: DeleteObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteObjectsCommand
 */
export declare const se_DeleteObjectsCommand: (input: DeleteObjectsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteObjectTaggingCommand
 */
export declare const se_DeleteObjectTaggingCommand: (input: DeleteObjectTaggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeletePublicAccessBlockCommand
 */
export declare const se_DeletePublicAccessBlockCommand: (input: DeletePublicAccessBlockCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketAccelerateConfigurationCommand
 */
export declare const se_GetBucketAccelerateConfigurationCommand: (input: GetBucketAccelerateConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketAclCommand
 */
export declare const se_GetBucketAclCommand: (input: GetBucketAclCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketAnalyticsConfigurationCommand
 */
export declare const se_GetBucketAnalyticsConfigurationCommand: (input: GetBucketAnalyticsConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketCorsCommand
 */
export declare const se_GetBucketCorsCommand: (input: GetBucketCorsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketEncryptionCommand
 */
export declare const se_GetBucketEncryptionCommand: (input: GetBucketEncryptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketIntelligentTieringConfigurationCommand
 */
export declare const se_GetBucketIntelligentTieringConfigurationCommand: (input: GetBucketIntelligentTieringConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketInventoryConfigurationCommand
 */
export declare const se_GetBucketInventoryConfigurationCommand: (input: GetBucketInventoryConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketLifecycleConfigurationCommand
 */
export declare const se_GetBucketLifecycleConfigurationCommand: (input: GetBucketLifecycleConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketLocationCommand
 */
export declare const se_GetBucketLocationCommand: (input: GetBucketLocationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketLoggingCommand
 */
export declare const se_GetBucketLoggingCommand: (input: GetBucketLoggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketMetadataTableConfigurationCommand
 */
export declare const se_GetBucketMetadataTableConfigurationCommand: (input: GetBucketMetadataTableConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketMetricsConfigurationCommand
 */
export declare const se_GetBucketMetricsConfigurationCommand: (input: GetBucketMetricsConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketNotificationConfigurationCommand
 */
export declare const se_GetBucketNotificationConfigurationCommand: (input: GetBucketNotificationConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketOwnershipControlsCommand
 */
export declare const se_GetBucketOwnershipControlsCommand: (input: GetBucketOwnershipControlsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketPolicyCommand
 */
export declare const se_GetBucketPolicyCommand: (input: GetBucketPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketPolicyStatusCommand
 */
export declare const se_GetBucketPolicyStatusCommand: (input: GetBucketPolicyStatusCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketReplicationCommand
 */
export declare const se_GetBucketReplicationCommand: (input: GetBucketReplicationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketRequestPaymentCommand
 */
export declare const se_GetBucketRequestPaymentCommand: (input: GetBucketRequestPaymentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketTaggingCommand
 */
export declare const se_GetBucketTaggingCommand: (input: GetBucketTaggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketVersioningCommand
 */
export declare const se_GetBucketVersioningCommand: (input: GetBucketVersioningCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetBucketWebsiteCommand
 */
export declare const se_GetBucketWebsiteCommand: (input: GetBucketWebsiteCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectCommand
 */
export declare const se_GetObjectCommand: (input: GetObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectAclCommand
 */
export declare const se_GetObjectAclCommand: (input: GetObjectAclCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectAttributesCommand
 */
export declare const se_GetObjectAttributesCommand: (input: GetObjectAttributesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectLegalHoldCommand
 */
export declare const se_GetObjectLegalHoldCommand: (input: GetObjectLegalHoldCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectLockConfigurationCommand
 */
export declare const se_GetObjectLockConfigurationCommand: (input: GetObjectLockConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectRetentionCommand
 */
export declare const se_GetObjectRetentionCommand: (input: GetObjectRetentionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectTaggingCommand
 */
export declare const se_GetObjectTaggingCommand: (input: GetObjectTaggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetObjectTorrentCommand
 */
export declare const se_GetObjectTorrentCommand: (input: GetObjectTorrentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetPublicAccessBlockCommand
 */
export declare const se_GetPublicAccessBlockCommand: (input: GetPublicAccessBlockCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlHeadBucketCommand
 */
export declare const se_HeadBucketCommand: (input: HeadBucketCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlHeadObjectCommand
 */
export declare const se_HeadObjectCommand: (input: HeadObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListBucketAnalyticsConfigurationsCommand
 */
export declare const se_ListBucketAnalyticsConfigurationsCommand: (input: ListBucketAnalyticsConfigurationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListBucketIntelligentTieringConfigurationsCommand
 */
export declare const se_ListBucketIntelligentTieringConfigurationsCommand: (input: ListBucketIntelligentTieringConfigurationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListBucketInventoryConfigurationsCommand
 */
export declare const se_ListBucketInventoryConfigurationsCommand: (input: ListBucketInventoryConfigurationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListBucketMetricsConfigurationsCommand
 */
export declare const se_ListBucketMetricsConfigurationsCommand: (input: ListBucketMetricsConfigurationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListBucketsCommand
 */
export declare const se_ListBucketsCommand: (input: ListBucketsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDirectoryBucketsCommand
 */
export declare const se_ListDirectoryBucketsCommand: (input: ListDirectoryBucketsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListMultipartUploadsCommand
 */
export declare const se_ListMultipartUploadsCommand: (input: ListMultipartUploadsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListObjectsCommand
 */
export declare const se_ListObjectsCommand: (input: ListObjectsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListObjectsV2Command
 */
export declare const se_ListObjectsV2Command: (input: ListObjectsV2CommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListObjectVersionsCommand
 */
export declare const se_ListObjectVersionsCommand: (input: ListObjectVersionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListPartsCommand
 */
export declare const se_ListPartsCommand: (input: ListPartsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketAccelerateConfigurationCommand
 */
export declare const se_PutBucketAccelerateConfigurationCommand: (input: PutBucketAccelerateConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketAclCommand
 */
export declare const se_PutBucketAclCommand: (input: PutBucketAclCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketAnalyticsConfigurationCommand
 */
export declare const se_PutBucketAnalyticsConfigurationCommand: (input: PutBucketAnalyticsConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketCorsCommand
 */
export declare const se_PutBucketCorsCommand: (input: PutBucketCorsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketEncryptionCommand
 */
export declare const se_PutBucketEncryptionCommand: (input: PutBucketEncryptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketIntelligentTieringConfigurationCommand
 */
export declare const se_PutBucketIntelligentTieringConfigurationCommand: (input: PutBucketIntelligentTieringConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketInventoryConfigurationCommand
 */
export declare const se_PutBucketInventoryConfigurationCommand: (input: PutBucketInventoryConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketLifecycleConfigurationCommand
 */
export declare const se_PutBucketLifecycleConfigurationCommand: (input: PutBucketLifecycleConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketLoggingCommand
 */
export declare const se_PutBucketLoggingCommand: (input: PutBucketLoggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketMetricsConfigurationCommand
 */
export declare const se_PutBucketMetricsConfigurationCommand: (input: PutBucketMetricsConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketNotificationConfigurationCommand
 */
export declare const se_PutBucketNotificationConfigurationCommand: (input: PutBucketNotificationConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketOwnershipControlsCommand
 */
export declare const se_PutBucketOwnershipControlsCommand: (input: PutBucketOwnershipControlsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketPolicyCommand
 */
export declare const se_PutBucketPolicyCommand: (input: PutBucketPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketReplicationCommand
 */
export declare const se_PutBucketReplicationCommand: (input: PutBucketReplicationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketRequestPaymentCommand
 */
export declare const se_PutBucketRequestPaymentCommand: (input: PutBucketRequestPaymentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketTaggingCommand
 */
export declare const se_PutBucketTaggingCommand: (input: PutBucketTaggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketVersioningCommand
 */
export declare const se_PutBucketVersioningCommand: (input: PutBucketVersioningCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutBucketWebsiteCommand
 */
export declare const se_PutBucketWebsiteCommand: (input: PutBucketWebsiteCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutObjectCommand
 */
export declare const se_PutObjectCommand: (input: PutObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutObjectAclCommand
 */
export declare const se_PutObjectAclCommand: (input: PutObjectAclCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutObjectLegalHoldCommand
 */
export declare const se_PutObjectLegalHoldCommand: (input: PutObjectLegalHoldCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutObjectLockConfigurationCommand
 */
export declare const se_PutObjectLockConfigurationCommand: (input: PutObjectLockConfigurationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutObjectRetentionCommand
 */
export declare const se_PutObjectRetentionCommand: (input: PutObjectRetentionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutObjectTaggingCommand
 */
export declare const se_PutObjectTaggingCommand: (input: PutObjectTaggingCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPutPublicAccessBlockCommand
 */
export declare const se_PutPublicAccessBlockCommand: (input: PutPublicAccessBlockCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlRenameObjectCommand
 */
export declare const se_RenameObjectCommand: (input: RenameObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlRestoreObjectCommand
 */
export declare const se_RestoreObjectCommand: (input: RestoreObjectCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlSelectObjectContentCommand
 */
export declare const se_SelectObjectContentCommand: (input: SelectObjectContentCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUploadPartCommand
 */
export declare const se_UploadPartCommand: (input: UploadPartCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUploadPartCopyCommand
 */
export declare const se_UploadPartCopyCommand: (input: UploadPartCopyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlWriteGetObjectResponseCommand
 */
export declare const se_WriteGetObjectResponseCommand: (input: WriteGetObjectResponseCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restXmlAbortMultipartUploadCommand
 */
export declare const de_AbortMultipartUploadCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AbortMultipartUploadCommandOutput>;
/**
 * deserializeAws_restXmlCompleteMultipartUploadCommand
 */
export declare const de_CompleteMultipartUploadCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CompleteMultipartUploadCommandOutput>;
/**
 * deserializeAws_restXmlCopyObjectCommand
 */
export declare const de_CopyObjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CopyObjectCommandOutput>;
/**
 * deserializeAws_restXmlCreateBucketCommand
 */
export declare const de_CreateBucketCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateBucketCommandOutput>;
/**
 * deserializeAws_restXmlCreateBucketMetadataTableConfigurationCommand
 */
export declare const de_CreateBucketMetadataTableConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateBucketMetadataTableConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlCreateMultipartUploadCommand
 */
export declare const de_CreateMultipartUploadCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateMultipartUploadCommandOutput>;
/**
 * deserializeAws_restXmlCreateSessionCommand
 */
export declare const de_CreateSessionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateSessionCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketCommand
 */
export declare const de_DeleteBucketCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketAnalyticsConfigurationCommand
 */
export declare const de_DeleteBucketAnalyticsConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketAnalyticsConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketCorsCommand
 */
export declare const de_DeleteBucketCorsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketCorsCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketEncryptionCommand
 */
export declare const de_DeleteBucketEncryptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketEncryptionCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketIntelligentTieringConfigurationCommand
 */
export declare const de_DeleteBucketIntelligentTieringConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketIntelligentTieringConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketInventoryConfigurationCommand
 */
export declare const de_DeleteBucketInventoryConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketInventoryConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketLifecycleCommand
 */
export declare const de_DeleteBucketLifecycleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketLifecycleCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketMetadataTableConfigurationCommand
 */
export declare const de_DeleteBucketMetadataTableConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketMetadataTableConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketMetricsConfigurationCommand
 */
export declare const de_DeleteBucketMetricsConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketMetricsConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketOwnershipControlsCommand
 */
export declare const de_DeleteBucketOwnershipControlsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketOwnershipControlsCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketPolicyCommand
 */
export declare const de_DeleteBucketPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketPolicyCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketReplicationCommand
 */
export declare const de_DeleteBucketReplicationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketReplicationCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketTaggingCommand
 */
export declare const de_DeleteBucketTaggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketTaggingCommandOutput>;
/**
 * deserializeAws_restXmlDeleteBucketWebsiteCommand
 */
export declare const de_DeleteBucketWebsiteCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteBucketWebsiteCommandOutput>;
/**
 * deserializeAws_restXmlDeleteObjectCommand
 */
export declare const de_DeleteObjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteObjectCommandOutput>;
/**
 * deserializeAws_restXmlDeleteObjectsCommand
 */
export declare const de_DeleteObjectsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteObjectsCommandOutput>;
/**
 * deserializeAws_restXmlDeleteObjectTaggingCommand
 */
export declare const de_DeleteObjectTaggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteObjectTaggingCommandOutput>;
/**
 * deserializeAws_restXmlDeletePublicAccessBlockCommand
 */
export declare const de_DeletePublicAccessBlockCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeletePublicAccessBlockCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketAccelerateConfigurationCommand
 */
export declare const de_GetBucketAccelerateConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketAccelerateConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketAclCommand
 */
export declare const de_GetBucketAclCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketAclCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketAnalyticsConfigurationCommand
 */
export declare const de_GetBucketAnalyticsConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketAnalyticsConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketCorsCommand
 */
export declare const de_GetBucketCorsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketCorsCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketEncryptionCommand
 */
export declare const de_GetBucketEncryptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketEncryptionCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketIntelligentTieringConfigurationCommand
 */
export declare const de_GetBucketIntelligentTieringConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketIntelligentTieringConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketInventoryConfigurationCommand
 */
export declare const de_GetBucketInventoryConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketInventoryConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketLifecycleConfigurationCommand
 */
export declare const de_GetBucketLifecycleConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketLifecycleConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketLocationCommand
 */
export declare const de_GetBucketLocationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketLocationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketLoggingCommand
 */
export declare const de_GetBucketLoggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketLoggingCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketMetadataTableConfigurationCommand
 */
export declare const de_GetBucketMetadataTableConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketMetadataTableConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketMetricsConfigurationCommand
 */
export declare const de_GetBucketMetricsConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketMetricsConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketNotificationConfigurationCommand
 */
export declare const de_GetBucketNotificationConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketNotificationConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketOwnershipControlsCommand
 */
export declare const de_GetBucketOwnershipControlsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketOwnershipControlsCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketPolicyCommand
 */
export declare const de_GetBucketPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketPolicyCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketPolicyStatusCommand
 */
export declare const de_GetBucketPolicyStatusCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketPolicyStatusCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketReplicationCommand
 */
export declare const de_GetBucketReplicationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketReplicationCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketRequestPaymentCommand
 */
export declare const de_GetBucketRequestPaymentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketRequestPaymentCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketTaggingCommand
 */
export declare const de_GetBucketTaggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketTaggingCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketVersioningCommand
 */
export declare const de_GetBucketVersioningCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketVersioningCommandOutput>;
/**
 * deserializeAws_restXmlGetBucketWebsiteCommand
 */
export declare const de_GetBucketWebsiteCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetBucketWebsiteCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectCommand
 */
export declare const de_GetObjectCommand: (output: __HttpResponse, context: __SerdeContext & __SdkStreamSerdeContext) => Promise<GetObjectCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectAclCommand
 */
export declare const de_GetObjectAclCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetObjectAclCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectAttributesCommand
 */
export declare const de_GetObjectAttributesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetObjectAttributesCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectLegalHoldCommand
 */
export declare const de_GetObjectLegalHoldCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetObjectLegalHoldCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectLockConfigurationCommand
 */
export declare const de_GetObjectLockConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetObjectLockConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectRetentionCommand
 */
export declare const de_GetObjectRetentionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetObjectRetentionCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectTaggingCommand
 */
export declare const de_GetObjectTaggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetObjectTaggingCommandOutput>;
/**
 * deserializeAws_restXmlGetObjectTorrentCommand
 */
export declare const de_GetObjectTorrentCommand: (output: __HttpResponse, context: __SerdeContext & __SdkStreamSerdeContext) => Promise<GetObjectTorrentCommandOutput>;
/**
 * deserializeAws_restXmlGetPublicAccessBlockCommand
 */
export declare const de_GetPublicAccessBlockCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetPublicAccessBlockCommandOutput>;
/**
 * deserializeAws_restXmlHeadBucketCommand
 */
export declare const de_HeadBucketCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<HeadBucketCommandOutput>;
/**
 * deserializeAws_restXmlHeadObjectCommand
 */
export declare const de_HeadObjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<HeadObjectCommandOutput>;
/**
 * deserializeAws_restXmlListBucketAnalyticsConfigurationsCommand
 */
export declare const de_ListBucketAnalyticsConfigurationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListBucketAnalyticsConfigurationsCommandOutput>;
/**
 * deserializeAws_restXmlListBucketIntelligentTieringConfigurationsCommand
 */
export declare const de_ListBucketIntelligentTieringConfigurationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListBucketIntelligentTieringConfigurationsCommandOutput>;
/**
 * deserializeAws_restXmlListBucketInventoryConfigurationsCommand
 */
export declare const de_ListBucketInventoryConfigurationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListBucketInventoryConfigurationsCommandOutput>;
/**
 * deserializeAws_restXmlListBucketMetricsConfigurationsCommand
 */
export declare const de_ListBucketMetricsConfigurationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListBucketMetricsConfigurationsCommandOutput>;
/**
 * deserializeAws_restXmlListBucketsCommand
 */
export declare const de_ListBucketsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListBucketsCommandOutput>;
/**
 * deserializeAws_restXmlListDirectoryBucketsCommand
 */
export declare const de_ListDirectoryBucketsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDirectoryBucketsCommandOutput>;
/**
 * deserializeAws_restXmlListMultipartUploadsCommand
 */
export declare const de_ListMultipartUploadsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListMultipartUploadsCommandOutput>;
/**
 * deserializeAws_restXmlListObjectsCommand
 */
export declare const de_ListObjectsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListObjectsCommandOutput>;
/**
 * deserializeAws_restXmlListObjectsV2Command
 */
export declare const de_ListObjectsV2Command: (output: __HttpResponse, context: __SerdeContext) => Promise<ListObjectsV2CommandOutput>;
/**
 * deserializeAws_restXmlListObjectVersionsCommand
 */
export declare const de_ListObjectVersionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListObjectVersionsCommandOutput>;
/**
 * deserializeAws_restXmlListPartsCommand
 */
export declare const de_ListPartsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPartsCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketAccelerateConfigurationCommand
 */
export declare const de_PutBucketAccelerateConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketAccelerateConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketAclCommand
 */
export declare const de_PutBucketAclCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketAclCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketAnalyticsConfigurationCommand
 */
export declare const de_PutBucketAnalyticsConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketAnalyticsConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketCorsCommand
 */
export declare const de_PutBucketCorsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketCorsCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketEncryptionCommand
 */
export declare const de_PutBucketEncryptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketEncryptionCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketIntelligentTieringConfigurationCommand
 */
export declare const de_PutBucketIntelligentTieringConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketIntelligentTieringConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketInventoryConfigurationCommand
 */
export declare const de_PutBucketInventoryConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketInventoryConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketLifecycleConfigurationCommand
 */
export declare const de_PutBucketLifecycleConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketLifecycleConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketLoggingCommand
 */
export declare const de_PutBucketLoggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketLoggingCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketMetricsConfigurationCommand
 */
export declare const de_PutBucketMetricsConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketMetricsConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketNotificationConfigurationCommand
 */
export declare const de_PutBucketNotificationConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketNotificationConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketOwnershipControlsCommand
 */
export declare const de_PutBucketOwnershipControlsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketOwnershipControlsCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketPolicyCommand
 */
export declare const de_PutBucketPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketPolicyCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketReplicationCommand
 */
export declare const de_PutBucketReplicationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketReplicationCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketRequestPaymentCommand
 */
export declare const de_PutBucketRequestPaymentCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketRequestPaymentCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketTaggingCommand
 */
export declare const de_PutBucketTaggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketTaggingCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketVersioningCommand
 */
export declare const de_PutBucketVersioningCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketVersioningCommandOutput>;
/**
 * deserializeAws_restXmlPutBucketWebsiteCommand
 */
export declare const de_PutBucketWebsiteCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutBucketWebsiteCommandOutput>;
/**
 * deserializeAws_restXmlPutObjectCommand
 */
export declare const de_PutObjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutObjectCommandOutput>;
/**
 * deserializeAws_restXmlPutObjectAclCommand
 */
export declare const de_PutObjectAclCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutObjectAclCommandOutput>;
/**
 * deserializeAws_restXmlPutObjectLegalHoldCommand
 */
export declare const de_PutObjectLegalHoldCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutObjectLegalHoldCommandOutput>;
/**
 * deserializeAws_restXmlPutObjectLockConfigurationCommand
 */
export declare const de_PutObjectLockConfigurationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutObjectLockConfigurationCommandOutput>;
/**
 * deserializeAws_restXmlPutObjectRetentionCommand
 */
export declare const de_PutObjectRetentionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutObjectRetentionCommandOutput>;
/**
 * deserializeAws_restXmlPutObjectTaggingCommand
 */
export declare const de_PutObjectTaggingCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutObjectTaggingCommandOutput>;
/**
 * deserializeAws_restXmlPutPublicAccessBlockCommand
 */
export declare const de_PutPublicAccessBlockCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutPublicAccessBlockCommandOutput>;
/**
 * deserializeAws_restXmlRenameObjectCommand
 */
export declare const de_RenameObjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<RenameObjectCommandOutput>;
/**
 * deserializeAws_restXmlRestoreObjectCommand
 */
export declare const de_RestoreObjectCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<RestoreObjectCommandOutput>;
/**
 * deserializeAws_restXmlSelectObjectContentCommand
 */
export declare const de_SelectObjectContentCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<SelectObjectContentCommandOutput>;
/**
 * deserializeAws_restXmlUploadPartCommand
 */
export declare const de_UploadPartCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UploadPartCommandOutput>;
/**
 * deserializeAws_restXmlUploadPartCopyCommand
 */
export declare const de_UploadPartCopyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UploadPartCopyCommandOutput>;
/**
 * deserializeAws_restXmlWriteGetObjectResponseCommand
 */
export declare const de_WriteGetObjectResponseCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<WriteGetObjectResponseCommandOutput>;
