import type { HttpResponse, SerdeContext } from "@smithy/types";
/**
 * @internal
 */
export declare const parseXmlBody: (streamBody: any, context: SerdeContext) => any;
/**
 * @internal
 */
export declare const parseXmlErrorBody: (errorBody: any, context: SerdeContext) => Promise<any>;
/**
 * @internal
 */
export declare const loadRestXmlErrorCode: (output: HttpResponse, data: any) => string | undefined;
