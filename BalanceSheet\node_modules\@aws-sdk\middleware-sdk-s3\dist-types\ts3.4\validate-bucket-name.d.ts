import {
  InitializeHandlerOptions,
  InitializeMiddleware,
  Pluggable,
} from "@smithy/types";
import { S3ResolvedConfig } from "./s3Configuration";
export declare function validateBucketNameMiddleware({
  bucketEndpoint,
}: S3ResolvedConfig): InitializeMiddleware<any, any>;
export declare const validateBucketNameMiddlewareOptions: InitializeHandlerOptions;
export declare const getValidateBucketNamePlugin: (
  options: S3ResolvedConfig
) => Pluggable<any, any>;
