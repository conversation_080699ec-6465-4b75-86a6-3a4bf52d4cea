import {
  DeserializeMiddleware,
  RelativeMiddlewareOptions,
} from "@smithy/types";
import { PreviouslyResolved } from "./configuration";
export interface FlexibleChecksumsResponseMiddlewareConfig {
  requestValidationModeMember?: string;
  responseAlgorithms?: string[];
}
export declare const flexibleChecksumsResponseMiddlewareOptions: RelativeMiddlewareOptions;
export declare const flexibleChecksumsResponseMiddleware: (
  config: PreviouslyResolved,
  middlewareConfig: FlexibleChecksumsResponseMiddlewareConfig
) => DeserializeMiddleware<any, any>;
