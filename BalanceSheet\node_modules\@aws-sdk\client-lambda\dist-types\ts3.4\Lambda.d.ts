import { HttpHandlerOptions as __HttpHandlerOptions } from "@smithy/types";
import {
  AddLayerVersionPermissionCommandInput,
  AddLayerVersionPermissionCommandOutput,
} from "./commands/AddLayerVersionPermissionCommand";
import {
  AddPermissionCommandInput,
  AddPermissionCommandOutput,
} from "./commands/AddPermissionCommand";
import {
  CreateAliasCommandInput,
  CreateAliasCommandOutput,
} from "./commands/CreateAliasCommand";
import {
  CreateCodeSigningConfigCommandInput,
  CreateCodeSigningConfigCommandOutput,
} from "./commands/CreateCodeSigningConfigCommand";
import {
  CreateEventSourceMappingCommandInput,
  CreateEventSourceMappingCommandOutput,
} from "./commands/CreateEventSourceMappingCommand";
import {
  CreateFunctionCommandInput,
  CreateFunctionCommandOutput,
} from "./commands/CreateFunctionCommand";
import {
  CreateFunctionUrlConfigCommandInput,
  CreateFunctionUrlConfigCommandOutput,
} from "./commands/CreateFunctionUrlConfigCommand";
import {
  DeleteAliasCommandInput,
  DeleteAliasCommandOutput,
} from "./commands/DeleteAliasCommand";
import {
  DeleteCodeSigningConfigCommandInput,
  DeleteCodeSigningConfigCommandOutput,
} from "./commands/DeleteCodeSigningConfigCommand";
import {
  DeleteEventSourceMappingCommandInput,
  DeleteEventSourceMappingCommandOutput,
} from "./commands/DeleteEventSourceMappingCommand";
import {
  DeleteFunctionCodeSigningConfigCommandInput,
  DeleteFunctionCodeSigningConfigCommandOutput,
} from "./commands/DeleteFunctionCodeSigningConfigCommand";
import {
  DeleteFunctionCommandInput,
  DeleteFunctionCommandOutput,
} from "./commands/DeleteFunctionCommand";
import {
  DeleteFunctionConcurrencyCommandInput,
  DeleteFunctionConcurrencyCommandOutput,
} from "./commands/DeleteFunctionConcurrencyCommand";
import {
  DeleteFunctionEventInvokeConfigCommandInput,
  DeleteFunctionEventInvokeConfigCommandOutput,
} from "./commands/DeleteFunctionEventInvokeConfigCommand";
import {
  DeleteFunctionUrlConfigCommandInput,
  DeleteFunctionUrlConfigCommandOutput,
} from "./commands/DeleteFunctionUrlConfigCommand";
import {
  DeleteLayerVersionCommandInput,
  DeleteLayerVersionCommandOutput,
} from "./commands/DeleteLayerVersionCommand";
import {
  DeleteProvisionedConcurrencyConfigCommandInput,
  DeleteProvisionedConcurrencyConfigCommandOutput,
} from "./commands/DeleteProvisionedConcurrencyConfigCommand";
import {
  GetAccountSettingsCommandInput,
  GetAccountSettingsCommandOutput,
} from "./commands/GetAccountSettingsCommand";
import {
  GetAliasCommandInput,
  GetAliasCommandOutput,
} from "./commands/GetAliasCommand";
import {
  GetCodeSigningConfigCommandInput,
  GetCodeSigningConfigCommandOutput,
} from "./commands/GetCodeSigningConfigCommand";
import {
  GetEventSourceMappingCommandInput,
  GetEventSourceMappingCommandOutput,
} from "./commands/GetEventSourceMappingCommand";
import {
  GetFunctionCodeSigningConfigCommandInput,
  GetFunctionCodeSigningConfigCommandOutput,
} from "./commands/GetFunctionCodeSigningConfigCommand";
import {
  GetFunctionCommandInput,
  GetFunctionCommandOutput,
} from "./commands/GetFunctionCommand";
import {
  GetFunctionConcurrencyCommandInput,
  GetFunctionConcurrencyCommandOutput,
} from "./commands/GetFunctionConcurrencyCommand";
import {
  GetFunctionConfigurationCommandInput,
  GetFunctionConfigurationCommandOutput,
} from "./commands/GetFunctionConfigurationCommand";
import {
  GetFunctionEventInvokeConfigCommandInput,
  GetFunctionEventInvokeConfigCommandOutput,
} from "./commands/GetFunctionEventInvokeConfigCommand";
import {
  GetFunctionRecursionConfigCommandInput,
  GetFunctionRecursionConfigCommandOutput,
} from "./commands/GetFunctionRecursionConfigCommand";
import {
  GetFunctionUrlConfigCommandInput,
  GetFunctionUrlConfigCommandOutput,
} from "./commands/GetFunctionUrlConfigCommand";
import {
  GetLayerVersionByArnCommandInput,
  GetLayerVersionByArnCommandOutput,
} from "./commands/GetLayerVersionByArnCommand";
import {
  GetLayerVersionCommandInput,
  GetLayerVersionCommandOutput,
} from "./commands/GetLayerVersionCommand";
import {
  GetLayerVersionPolicyCommandInput,
  GetLayerVersionPolicyCommandOutput,
} from "./commands/GetLayerVersionPolicyCommand";
import {
  GetPolicyCommandInput,
  GetPolicyCommandOutput,
} from "./commands/GetPolicyCommand";
import {
  GetProvisionedConcurrencyConfigCommandInput,
  GetProvisionedConcurrencyConfigCommandOutput,
} from "./commands/GetProvisionedConcurrencyConfigCommand";
import {
  GetRuntimeManagementConfigCommandInput,
  GetRuntimeManagementConfigCommandOutput,
} from "./commands/GetRuntimeManagementConfigCommand";
import {
  InvokeAsyncCommandInput,
  InvokeAsyncCommandOutput,
} from "./commands/InvokeAsyncCommand";
import {
  InvokeCommandInput,
  InvokeCommandOutput,
} from "./commands/InvokeCommand";
import {
  InvokeWithResponseStreamCommandInput,
  InvokeWithResponseStreamCommandOutput,
} from "./commands/InvokeWithResponseStreamCommand";
import {
  ListAliasesCommandInput,
  ListAliasesCommandOutput,
} from "./commands/ListAliasesCommand";
import {
  ListCodeSigningConfigsCommandInput,
  ListCodeSigningConfigsCommandOutput,
} from "./commands/ListCodeSigningConfigsCommand";
import {
  ListEventSourceMappingsCommandInput,
  ListEventSourceMappingsCommandOutput,
} from "./commands/ListEventSourceMappingsCommand";
import {
  ListFunctionEventInvokeConfigsCommandInput,
  ListFunctionEventInvokeConfigsCommandOutput,
} from "./commands/ListFunctionEventInvokeConfigsCommand";
import {
  ListFunctionsByCodeSigningConfigCommandInput,
  ListFunctionsByCodeSigningConfigCommandOutput,
} from "./commands/ListFunctionsByCodeSigningConfigCommand";
import {
  ListFunctionsCommandInput,
  ListFunctionsCommandOutput,
} from "./commands/ListFunctionsCommand";
import {
  ListFunctionUrlConfigsCommandInput,
  ListFunctionUrlConfigsCommandOutput,
} from "./commands/ListFunctionUrlConfigsCommand";
import {
  ListLayersCommandInput,
  ListLayersCommandOutput,
} from "./commands/ListLayersCommand";
import {
  ListLayerVersionsCommandInput,
  ListLayerVersionsCommandOutput,
} from "./commands/ListLayerVersionsCommand";
import {
  ListProvisionedConcurrencyConfigsCommandInput,
  ListProvisionedConcurrencyConfigsCommandOutput,
} from "./commands/ListProvisionedConcurrencyConfigsCommand";
import {
  ListTagsCommandInput,
  ListTagsCommandOutput,
} from "./commands/ListTagsCommand";
import {
  ListVersionsByFunctionCommandInput,
  ListVersionsByFunctionCommandOutput,
} from "./commands/ListVersionsByFunctionCommand";
import {
  PublishLayerVersionCommandInput,
  PublishLayerVersionCommandOutput,
} from "./commands/PublishLayerVersionCommand";
import {
  PublishVersionCommandInput,
  PublishVersionCommandOutput,
} from "./commands/PublishVersionCommand";
import {
  PutFunctionCodeSigningConfigCommandInput,
  PutFunctionCodeSigningConfigCommandOutput,
} from "./commands/PutFunctionCodeSigningConfigCommand";
import {
  PutFunctionConcurrencyCommandInput,
  PutFunctionConcurrencyCommandOutput,
} from "./commands/PutFunctionConcurrencyCommand";
import {
  PutFunctionEventInvokeConfigCommandInput,
  PutFunctionEventInvokeConfigCommandOutput,
} from "./commands/PutFunctionEventInvokeConfigCommand";
import {
  PutFunctionRecursionConfigCommandInput,
  PutFunctionRecursionConfigCommandOutput,
} from "./commands/PutFunctionRecursionConfigCommand";
import {
  PutProvisionedConcurrencyConfigCommandInput,
  PutProvisionedConcurrencyConfigCommandOutput,
} from "./commands/PutProvisionedConcurrencyConfigCommand";
import {
  PutRuntimeManagementConfigCommandInput,
  PutRuntimeManagementConfigCommandOutput,
} from "./commands/PutRuntimeManagementConfigCommand";
import {
  RemoveLayerVersionPermissionCommandInput,
  RemoveLayerVersionPermissionCommandOutput,
} from "./commands/RemoveLayerVersionPermissionCommand";
import {
  RemovePermissionCommandInput,
  RemovePermissionCommandOutput,
} from "./commands/RemovePermissionCommand";
import {
  TagResourceCommandInput,
  TagResourceCommandOutput,
} from "./commands/TagResourceCommand";
import {
  UntagResourceCommandInput,
  UntagResourceCommandOutput,
} from "./commands/UntagResourceCommand";
import {
  UpdateAliasCommandInput,
  UpdateAliasCommandOutput,
} from "./commands/UpdateAliasCommand";
import {
  UpdateCodeSigningConfigCommandInput,
  UpdateCodeSigningConfigCommandOutput,
} from "./commands/UpdateCodeSigningConfigCommand";
import {
  UpdateEventSourceMappingCommandInput,
  UpdateEventSourceMappingCommandOutput,
} from "./commands/UpdateEventSourceMappingCommand";
import {
  UpdateFunctionCodeCommandInput,
  UpdateFunctionCodeCommandOutput,
} from "./commands/UpdateFunctionCodeCommand";
import {
  UpdateFunctionConfigurationCommandInput,
  UpdateFunctionConfigurationCommandOutput,
} from "./commands/UpdateFunctionConfigurationCommand";
import {
  UpdateFunctionEventInvokeConfigCommandInput,
  UpdateFunctionEventInvokeConfigCommandOutput,
} from "./commands/UpdateFunctionEventInvokeConfigCommand";
import {
  UpdateFunctionUrlConfigCommandInput,
  UpdateFunctionUrlConfigCommandOutput,
} from "./commands/UpdateFunctionUrlConfigCommand";
import { LambdaClient } from "./LambdaClient";
export interface Lambda {
  addLayerVersionPermission(
    args: AddLayerVersionPermissionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<AddLayerVersionPermissionCommandOutput>;
  addLayerVersionPermission(
    args: AddLayerVersionPermissionCommandInput,
    cb: (err: any, data?: AddLayerVersionPermissionCommandOutput) => void
  ): void;
  addLayerVersionPermission(
    args: AddLayerVersionPermissionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: AddLayerVersionPermissionCommandOutput) => void
  ): void;
  addPermission(
    args: AddPermissionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<AddPermissionCommandOutput>;
  addPermission(
    args: AddPermissionCommandInput,
    cb: (err: any, data?: AddPermissionCommandOutput) => void
  ): void;
  addPermission(
    args: AddPermissionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: AddPermissionCommandOutput) => void
  ): void;
  createAlias(
    args: CreateAliasCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateAliasCommandOutput>;
  createAlias(
    args: CreateAliasCommandInput,
    cb: (err: any, data?: CreateAliasCommandOutput) => void
  ): void;
  createAlias(
    args: CreateAliasCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateAliasCommandOutput) => void
  ): void;
  createCodeSigningConfig(
    args: CreateCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateCodeSigningConfigCommandOutput>;
  createCodeSigningConfig(
    args: CreateCodeSigningConfigCommandInput,
    cb: (err: any, data?: CreateCodeSigningConfigCommandOutput) => void
  ): void;
  createCodeSigningConfig(
    args: CreateCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateCodeSigningConfigCommandOutput) => void
  ): void;
  createEventSourceMapping(
    args: CreateEventSourceMappingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateEventSourceMappingCommandOutput>;
  createEventSourceMapping(
    args: CreateEventSourceMappingCommandInput,
    cb: (err: any, data?: CreateEventSourceMappingCommandOutput) => void
  ): void;
  createEventSourceMapping(
    args: CreateEventSourceMappingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateEventSourceMappingCommandOutput) => void
  ): void;
  createFunction(
    args: CreateFunctionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateFunctionCommandOutput>;
  createFunction(
    args: CreateFunctionCommandInput,
    cb: (err: any, data?: CreateFunctionCommandOutput) => void
  ): void;
  createFunction(
    args: CreateFunctionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateFunctionCommandOutput) => void
  ): void;
  createFunctionUrlConfig(
    args: CreateFunctionUrlConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateFunctionUrlConfigCommandOutput>;
  createFunctionUrlConfig(
    args: CreateFunctionUrlConfigCommandInput,
    cb: (err: any, data?: CreateFunctionUrlConfigCommandOutput) => void
  ): void;
  createFunctionUrlConfig(
    args: CreateFunctionUrlConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateFunctionUrlConfigCommandOutput) => void
  ): void;
  deleteAlias(
    args: DeleteAliasCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteAliasCommandOutput>;
  deleteAlias(
    args: DeleteAliasCommandInput,
    cb: (err: any, data?: DeleteAliasCommandOutput) => void
  ): void;
  deleteAlias(
    args: DeleteAliasCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteAliasCommandOutput) => void
  ): void;
  deleteCodeSigningConfig(
    args: DeleteCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteCodeSigningConfigCommandOutput>;
  deleteCodeSigningConfig(
    args: DeleteCodeSigningConfigCommandInput,
    cb: (err: any, data?: DeleteCodeSigningConfigCommandOutput) => void
  ): void;
  deleteCodeSigningConfig(
    args: DeleteCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteCodeSigningConfigCommandOutput) => void
  ): void;
  deleteEventSourceMapping(
    args: DeleteEventSourceMappingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteEventSourceMappingCommandOutput>;
  deleteEventSourceMapping(
    args: DeleteEventSourceMappingCommandInput,
    cb: (err: any, data?: DeleteEventSourceMappingCommandOutput) => void
  ): void;
  deleteEventSourceMapping(
    args: DeleteEventSourceMappingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteEventSourceMappingCommandOutput) => void
  ): void;
  deleteFunction(
    args: DeleteFunctionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteFunctionCommandOutput>;
  deleteFunction(
    args: DeleteFunctionCommandInput,
    cb: (err: any, data?: DeleteFunctionCommandOutput) => void
  ): void;
  deleteFunction(
    args: DeleteFunctionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteFunctionCommandOutput) => void
  ): void;
  deleteFunctionCodeSigningConfig(
    args: DeleteFunctionCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteFunctionCodeSigningConfigCommandOutput>;
  deleteFunctionCodeSigningConfig(
    args: DeleteFunctionCodeSigningConfigCommandInput,
    cb: (err: any, data?: DeleteFunctionCodeSigningConfigCommandOutput) => void
  ): void;
  deleteFunctionCodeSigningConfig(
    args: DeleteFunctionCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteFunctionCodeSigningConfigCommandOutput) => void
  ): void;
  deleteFunctionConcurrency(
    args: DeleteFunctionConcurrencyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteFunctionConcurrencyCommandOutput>;
  deleteFunctionConcurrency(
    args: DeleteFunctionConcurrencyCommandInput,
    cb: (err: any, data?: DeleteFunctionConcurrencyCommandOutput) => void
  ): void;
  deleteFunctionConcurrency(
    args: DeleteFunctionConcurrencyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteFunctionConcurrencyCommandOutput) => void
  ): void;
  deleteFunctionEventInvokeConfig(
    args: DeleteFunctionEventInvokeConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteFunctionEventInvokeConfigCommandOutput>;
  deleteFunctionEventInvokeConfig(
    args: DeleteFunctionEventInvokeConfigCommandInput,
    cb: (err: any, data?: DeleteFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  deleteFunctionEventInvokeConfig(
    args: DeleteFunctionEventInvokeConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  deleteFunctionUrlConfig(
    args: DeleteFunctionUrlConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteFunctionUrlConfigCommandOutput>;
  deleteFunctionUrlConfig(
    args: DeleteFunctionUrlConfigCommandInput,
    cb: (err: any, data?: DeleteFunctionUrlConfigCommandOutput) => void
  ): void;
  deleteFunctionUrlConfig(
    args: DeleteFunctionUrlConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteFunctionUrlConfigCommandOutput) => void
  ): void;
  deleteLayerVersion(
    args: DeleteLayerVersionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteLayerVersionCommandOutput>;
  deleteLayerVersion(
    args: DeleteLayerVersionCommandInput,
    cb: (err: any, data?: DeleteLayerVersionCommandOutput) => void
  ): void;
  deleteLayerVersion(
    args: DeleteLayerVersionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteLayerVersionCommandOutput) => void
  ): void;
  deleteProvisionedConcurrencyConfig(
    args: DeleteProvisionedConcurrencyConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteProvisionedConcurrencyConfigCommandOutput>;
  deleteProvisionedConcurrencyConfig(
    args: DeleteProvisionedConcurrencyConfigCommandInput,
    cb: (
      err: any,
      data?: DeleteProvisionedConcurrencyConfigCommandOutput
    ) => void
  ): void;
  deleteProvisionedConcurrencyConfig(
    args: DeleteProvisionedConcurrencyConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: DeleteProvisionedConcurrencyConfigCommandOutput
    ) => void
  ): void;
  getAccountSettings(): Promise<GetAccountSettingsCommandOutput>;
  getAccountSettings(
    args: GetAccountSettingsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetAccountSettingsCommandOutput>;
  getAccountSettings(
    args: GetAccountSettingsCommandInput,
    cb: (err: any, data?: GetAccountSettingsCommandOutput) => void
  ): void;
  getAccountSettings(
    args: GetAccountSettingsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetAccountSettingsCommandOutput) => void
  ): void;
  getAlias(
    args: GetAliasCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetAliasCommandOutput>;
  getAlias(
    args: GetAliasCommandInput,
    cb: (err: any, data?: GetAliasCommandOutput) => void
  ): void;
  getAlias(
    args: GetAliasCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetAliasCommandOutput) => void
  ): void;
  getCodeSigningConfig(
    args: GetCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetCodeSigningConfigCommandOutput>;
  getCodeSigningConfig(
    args: GetCodeSigningConfigCommandInput,
    cb: (err: any, data?: GetCodeSigningConfigCommandOutput) => void
  ): void;
  getCodeSigningConfig(
    args: GetCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetCodeSigningConfigCommandOutput) => void
  ): void;
  getEventSourceMapping(
    args: GetEventSourceMappingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetEventSourceMappingCommandOutput>;
  getEventSourceMapping(
    args: GetEventSourceMappingCommandInput,
    cb: (err: any, data?: GetEventSourceMappingCommandOutput) => void
  ): void;
  getEventSourceMapping(
    args: GetEventSourceMappingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetEventSourceMappingCommandOutput) => void
  ): void;
  getFunction(
    args: GetFunctionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionCommandOutput>;
  getFunction(
    args: GetFunctionCommandInput,
    cb: (err: any, data?: GetFunctionCommandOutput) => void
  ): void;
  getFunction(
    args: GetFunctionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionCommandOutput) => void
  ): void;
  getFunctionCodeSigningConfig(
    args: GetFunctionCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionCodeSigningConfigCommandOutput>;
  getFunctionCodeSigningConfig(
    args: GetFunctionCodeSigningConfigCommandInput,
    cb: (err: any, data?: GetFunctionCodeSigningConfigCommandOutput) => void
  ): void;
  getFunctionCodeSigningConfig(
    args: GetFunctionCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionCodeSigningConfigCommandOutput) => void
  ): void;
  getFunctionConcurrency(
    args: GetFunctionConcurrencyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionConcurrencyCommandOutput>;
  getFunctionConcurrency(
    args: GetFunctionConcurrencyCommandInput,
    cb: (err: any, data?: GetFunctionConcurrencyCommandOutput) => void
  ): void;
  getFunctionConcurrency(
    args: GetFunctionConcurrencyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionConcurrencyCommandOutput) => void
  ): void;
  getFunctionConfiguration(
    args: GetFunctionConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionConfigurationCommandOutput>;
  getFunctionConfiguration(
    args: GetFunctionConfigurationCommandInput,
    cb: (err: any, data?: GetFunctionConfigurationCommandOutput) => void
  ): void;
  getFunctionConfiguration(
    args: GetFunctionConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionConfigurationCommandOutput) => void
  ): void;
  getFunctionEventInvokeConfig(
    args: GetFunctionEventInvokeConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionEventInvokeConfigCommandOutput>;
  getFunctionEventInvokeConfig(
    args: GetFunctionEventInvokeConfigCommandInput,
    cb: (err: any, data?: GetFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  getFunctionEventInvokeConfig(
    args: GetFunctionEventInvokeConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  getFunctionRecursionConfig(
    args: GetFunctionRecursionConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionRecursionConfigCommandOutput>;
  getFunctionRecursionConfig(
    args: GetFunctionRecursionConfigCommandInput,
    cb: (err: any, data?: GetFunctionRecursionConfigCommandOutput) => void
  ): void;
  getFunctionRecursionConfig(
    args: GetFunctionRecursionConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionRecursionConfigCommandOutput) => void
  ): void;
  getFunctionUrlConfig(
    args: GetFunctionUrlConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetFunctionUrlConfigCommandOutput>;
  getFunctionUrlConfig(
    args: GetFunctionUrlConfigCommandInput,
    cb: (err: any, data?: GetFunctionUrlConfigCommandOutput) => void
  ): void;
  getFunctionUrlConfig(
    args: GetFunctionUrlConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetFunctionUrlConfigCommandOutput) => void
  ): void;
  getLayerVersion(
    args: GetLayerVersionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetLayerVersionCommandOutput>;
  getLayerVersion(
    args: GetLayerVersionCommandInput,
    cb: (err: any, data?: GetLayerVersionCommandOutput) => void
  ): void;
  getLayerVersion(
    args: GetLayerVersionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetLayerVersionCommandOutput) => void
  ): void;
  getLayerVersionByArn(
    args: GetLayerVersionByArnCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetLayerVersionByArnCommandOutput>;
  getLayerVersionByArn(
    args: GetLayerVersionByArnCommandInput,
    cb: (err: any, data?: GetLayerVersionByArnCommandOutput) => void
  ): void;
  getLayerVersionByArn(
    args: GetLayerVersionByArnCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetLayerVersionByArnCommandOutput) => void
  ): void;
  getLayerVersionPolicy(
    args: GetLayerVersionPolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetLayerVersionPolicyCommandOutput>;
  getLayerVersionPolicy(
    args: GetLayerVersionPolicyCommandInput,
    cb: (err: any, data?: GetLayerVersionPolicyCommandOutput) => void
  ): void;
  getLayerVersionPolicy(
    args: GetLayerVersionPolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetLayerVersionPolicyCommandOutput) => void
  ): void;
  getPolicy(
    args: GetPolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetPolicyCommandOutput>;
  getPolicy(
    args: GetPolicyCommandInput,
    cb: (err: any, data?: GetPolicyCommandOutput) => void
  ): void;
  getPolicy(
    args: GetPolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetPolicyCommandOutput) => void
  ): void;
  getProvisionedConcurrencyConfig(
    args: GetProvisionedConcurrencyConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetProvisionedConcurrencyConfigCommandOutput>;
  getProvisionedConcurrencyConfig(
    args: GetProvisionedConcurrencyConfigCommandInput,
    cb: (err: any, data?: GetProvisionedConcurrencyConfigCommandOutput) => void
  ): void;
  getProvisionedConcurrencyConfig(
    args: GetProvisionedConcurrencyConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetProvisionedConcurrencyConfigCommandOutput) => void
  ): void;
  getRuntimeManagementConfig(
    args: GetRuntimeManagementConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetRuntimeManagementConfigCommandOutput>;
  getRuntimeManagementConfig(
    args: GetRuntimeManagementConfigCommandInput,
    cb: (err: any, data?: GetRuntimeManagementConfigCommandOutput) => void
  ): void;
  getRuntimeManagementConfig(
    args: GetRuntimeManagementConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetRuntimeManagementConfigCommandOutput) => void
  ): void;
  invoke(
    args: InvokeCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<InvokeCommandOutput>;
  invoke(
    args: InvokeCommandInput,
    cb: (err: any, data?: InvokeCommandOutput) => void
  ): void;
  invoke(
    args: InvokeCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: InvokeCommandOutput) => void
  ): void;
  invokeAsync(
    args: InvokeAsyncCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<InvokeAsyncCommandOutput>;
  invokeAsync(
    args: InvokeAsyncCommandInput,
    cb: (err: any, data?: InvokeAsyncCommandOutput) => void
  ): void;
  invokeAsync(
    args: InvokeAsyncCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: InvokeAsyncCommandOutput) => void
  ): void;
  invokeWithResponseStream(
    args: InvokeWithResponseStreamCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<InvokeWithResponseStreamCommandOutput>;
  invokeWithResponseStream(
    args: InvokeWithResponseStreamCommandInput,
    cb: (err: any, data?: InvokeWithResponseStreamCommandOutput) => void
  ): void;
  invokeWithResponseStream(
    args: InvokeWithResponseStreamCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: InvokeWithResponseStreamCommandOutput) => void
  ): void;
  listAliases(
    args: ListAliasesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListAliasesCommandOutput>;
  listAliases(
    args: ListAliasesCommandInput,
    cb: (err: any, data?: ListAliasesCommandOutput) => void
  ): void;
  listAliases(
    args: ListAliasesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListAliasesCommandOutput) => void
  ): void;
  listCodeSigningConfigs(): Promise<ListCodeSigningConfigsCommandOutput>;
  listCodeSigningConfigs(
    args: ListCodeSigningConfigsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListCodeSigningConfigsCommandOutput>;
  listCodeSigningConfigs(
    args: ListCodeSigningConfigsCommandInput,
    cb: (err: any, data?: ListCodeSigningConfigsCommandOutput) => void
  ): void;
  listCodeSigningConfigs(
    args: ListCodeSigningConfigsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListCodeSigningConfigsCommandOutput) => void
  ): void;
  listEventSourceMappings(): Promise<ListEventSourceMappingsCommandOutput>;
  listEventSourceMappings(
    args: ListEventSourceMappingsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListEventSourceMappingsCommandOutput>;
  listEventSourceMappings(
    args: ListEventSourceMappingsCommandInput,
    cb: (err: any, data?: ListEventSourceMappingsCommandOutput) => void
  ): void;
  listEventSourceMappings(
    args: ListEventSourceMappingsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListEventSourceMappingsCommandOutput) => void
  ): void;
  listFunctionEventInvokeConfigs(
    args: ListFunctionEventInvokeConfigsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListFunctionEventInvokeConfigsCommandOutput>;
  listFunctionEventInvokeConfigs(
    args: ListFunctionEventInvokeConfigsCommandInput,
    cb: (err: any, data?: ListFunctionEventInvokeConfigsCommandOutput) => void
  ): void;
  listFunctionEventInvokeConfigs(
    args: ListFunctionEventInvokeConfigsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListFunctionEventInvokeConfigsCommandOutput) => void
  ): void;
  listFunctions(): Promise<ListFunctionsCommandOutput>;
  listFunctions(
    args: ListFunctionsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListFunctionsCommandOutput>;
  listFunctions(
    args: ListFunctionsCommandInput,
    cb: (err: any, data?: ListFunctionsCommandOutput) => void
  ): void;
  listFunctions(
    args: ListFunctionsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListFunctionsCommandOutput) => void
  ): void;
  listFunctionsByCodeSigningConfig(
    args: ListFunctionsByCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListFunctionsByCodeSigningConfigCommandOutput>;
  listFunctionsByCodeSigningConfig(
    args: ListFunctionsByCodeSigningConfigCommandInput,
    cb: (err: any, data?: ListFunctionsByCodeSigningConfigCommandOutput) => void
  ): void;
  listFunctionsByCodeSigningConfig(
    args: ListFunctionsByCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListFunctionsByCodeSigningConfigCommandOutput) => void
  ): void;
  listFunctionUrlConfigs(
    args: ListFunctionUrlConfigsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListFunctionUrlConfigsCommandOutput>;
  listFunctionUrlConfigs(
    args: ListFunctionUrlConfigsCommandInput,
    cb: (err: any, data?: ListFunctionUrlConfigsCommandOutput) => void
  ): void;
  listFunctionUrlConfigs(
    args: ListFunctionUrlConfigsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListFunctionUrlConfigsCommandOutput) => void
  ): void;
  listLayers(): Promise<ListLayersCommandOutput>;
  listLayers(
    args: ListLayersCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListLayersCommandOutput>;
  listLayers(
    args: ListLayersCommandInput,
    cb: (err: any, data?: ListLayersCommandOutput) => void
  ): void;
  listLayers(
    args: ListLayersCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListLayersCommandOutput) => void
  ): void;
  listLayerVersions(
    args: ListLayerVersionsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListLayerVersionsCommandOutput>;
  listLayerVersions(
    args: ListLayerVersionsCommandInput,
    cb: (err: any, data?: ListLayerVersionsCommandOutput) => void
  ): void;
  listLayerVersions(
    args: ListLayerVersionsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListLayerVersionsCommandOutput) => void
  ): void;
  listProvisionedConcurrencyConfigs(
    args: ListProvisionedConcurrencyConfigsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListProvisionedConcurrencyConfigsCommandOutput>;
  listProvisionedConcurrencyConfigs(
    args: ListProvisionedConcurrencyConfigsCommandInput,
    cb: (
      err: any,
      data?: ListProvisionedConcurrencyConfigsCommandOutput
    ) => void
  ): void;
  listProvisionedConcurrencyConfigs(
    args: ListProvisionedConcurrencyConfigsCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: ListProvisionedConcurrencyConfigsCommandOutput
    ) => void
  ): void;
  listTags(
    args: ListTagsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListTagsCommandOutput>;
  listTags(
    args: ListTagsCommandInput,
    cb: (err: any, data?: ListTagsCommandOutput) => void
  ): void;
  listTags(
    args: ListTagsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListTagsCommandOutput) => void
  ): void;
  listVersionsByFunction(
    args: ListVersionsByFunctionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListVersionsByFunctionCommandOutput>;
  listVersionsByFunction(
    args: ListVersionsByFunctionCommandInput,
    cb: (err: any, data?: ListVersionsByFunctionCommandOutput) => void
  ): void;
  listVersionsByFunction(
    args: ListVersionsByFunctionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListVersionsByFunctionCommandOutput) => void
  ): void;
  publishLayerVersion(
    args: PublishLayerVersionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PublishLayerVersionCommandOutput>;
  publishLayerVersion(
    args: PublishLayerVersionCommandInput,
    cb: (err: any, data?: PublishLayerVersionCommandOutput) => void
  ): void;
  publishLayerVersion(
    args: PublishLayerVersionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PublishLayerVersionCommandOutput) => void
  ): void;
  publishVersion(
    args: PublishVersionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PublishVersionCommandOutput>;
  publishVersion(
    args: PublishVersionCommandInput,
    cb: (err: any, data?: PublishVersionCommandOutput) => void
  ): void;
  publishVersion(
    args: PublishVersionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PublishVersionCommandOutput) => void
  ): void;
  putFunctionCodeSigningConfig(
    args: PutFunctionCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutFunctionCodeSigningConfigCommandOutput>;
  putFunctionCodeSigningConfig(
    args: PutFunctionCodeSigningConfigCommandInput,
    cb: (err: any, data?: PutFunctionCodeSigningConfigCommandOutput) => void
  ): void;
  putFunctionCodeSigningConfig(
    args: PutFunctionCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutFunctionCodeSigningConfigCommandOutput) => void
  ): void;
  putFunctionConcurrency(
    args: PutFunctionConcurrencyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutFunctionConcurrencyCommandOutput>;
  putFunctionConcurrency(
    args: PutFunctionConcurrencyCommandInput,
    cb: (err: any, data?: PutFunctionConcurrencyCommandOutput) => void
  ): void;
  putFunctionConcurrency(
    args: PutFunctionConcurrencyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutFunctionConcurrencyCommandOutput) => void
  ): void;
  putFunctionEventInvokeConfig(
    args: PutFunctionEventInvokeConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutFunctionEventInvokeConfigCommandOutput>;
  putFunctionEventInvokeConfig(
    args: PutFunctionEventInvokeConfigCommandInput,
    cb: (err: any, data?: PutFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  putFunctionEventInvokeConfig(
    args: PutFunctionEventInvokeConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  putFunctionRecursionConfig(
    args: PutFunctionRecursionConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutFunctionRecursionConfigCommandOutput>;
  putFunctionRecursionConfig(
    args: PutFunctionRecursionConfigCommandInput,
    cb: (err: any, data?: PutFunctionRecursionConfigCommandOutput) => void
  ): void;
  putFunctionRecursionConfig(
    args: PutFunctionRecursionConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutFunctionRecursionConfigCommandOutput) => void
  ): void;
  putProvisionedConcurrencyConfig(
    args: PutProvisionedConcurrencyConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutProvisionedConcurrencyConfigCommandOutput>;
  putProvisionedConcurrencyConfig(
    args: PutProvisionedConcurrencyConfigCommandInput,
    cb: (err: any, data?: PutProvisionedConcurrencyConfigCommandOutput) => void
  ): void;
  putProvisionedConcurrencyConfig(
    args: PutProvisionedConcurrencyConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutProvisionedConcurrencyConfigCommandOutput) => void
  ): void;
  putRuntimeManagementConfig(
    args: PutRuntimeManagementConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutRuntimeManagementConfigCommandOutput>;
  putRuntimeManagementConfig(
    args: PutRuntimeManagementConfigCommandInput,
    cb: (err: any, data?: PutRuntimeManagementConfigCommandOutput) => void
  ): void;
  putRuntimeManagementConfig(
    args: PutRuntimeManagementConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutRuntimeManagementConfigCommandOutput) => void
  ): void;
  removeLayerVersionPermission(
    args: RemoveLayerVersionPermissionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<RemoveLayerVersionPermissionCommandOutput>;
  removeLayerVersionPermission(
    args: RemoveLayerVersionPermissionCommandInput,
    cb: (err: any, data?: RemoveLayerVersionPermissionCommandOutput) => void
  ): void;
  removeLayerVersionPermission(
    args: RemoveLayerVersionPermissionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: RemoveLayerVersionPermissionCommandOutput) => void
  ): void;
  removePermission(
    args: RemovePermissionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<RemovePermissionCommandOutput>;
  removePermission(
    args: RemovePermissionCommandInput,
    cb: (err: any, data?: RemovePermissionCommandOutput) => void
  ): void;
  removePermission(
    args: RemovePermissionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: RemovePermissionCommandOutput) => void
  ): void;
  tagResource(
    args: TagResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<TagResourceCommandOutput>;
  tagResource(
    args: TagResourceCommandInput,
    cb: (err: any, data?: TagResourceCommandOutput) => void
  ): void;
  tagResource(
    args: TagResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: TagResourceCommandOutput) => void
  ): void;
  untagResource(
    args: UntagResourceCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UntagResourceCommandOutput>;
  untagResource(
    args: UntagResourceCommandInput,
    cb: (err: any, data?: UntagResourceCommandOutput) => void
  ): void;
  untagResource(
    args: UntagResourceCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UntagResourceCommandOutput) => void
  ): void;
  updateAlias(
    args: UpdateAliasCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateAliasCommandOutput>;
  updateAlias(
    args: UpdateAliasCommandInput,
    cb: (err: any, data?: UpdateAliasCommandOutput) => void
  ): void;
  updateAlias(
    args: UpdateAliasCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateAliasCommandOutput) => void
  ): void;
  updateCodeSigningConfig(
    args: UpdateCodeSigningConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateCodeSigningConfigCommandOutput>;
  updateCodeSigningConfig(
    args: UpdateCodeSigningConfigCommandInput,
    cb: (err: any, data?: UpdateCodeSigningConfigCommandOutput) => void
  ): void;
  updateCodeSigningConfig(
    args: UpdateCodeSigningConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateCodeSigningConfigCommandOutput) => void
  ): void;
  updateEventSourceMapping(
    args: UpdateEventSourceMappingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateEventSourceMappingCommandOutput>;
  updateEventSourceMapping(
    args: UpdateEventSourceMappingCommandInput,
    cb: (err: any, data?: UpdateEventSourceMappingCommandOutput) => void
  ): void;
  updateEventSourceMapping(
    args: UpdateEventSourceMappingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateEventSourceMappingCommandOutput) => void
  ): void;
  updateFunctionCode(
    args: UpdateFunctionCodeCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateFunctionCodeCommandOutput>;
  updateFunctionCode(
    args: UpdateFunctionCodeCommandInput,
    cb: (err: any, data?: UpdateFunctionCodeCommandOutput) => void
  ): void;
  updateFunctionCode(
    args: UpdateFunctionCodeCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateFunctionCodeCommandOutput) => void
  ): void;
  updateFunctionConfiguration(
    args: UpdateFunctionConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateFunctionConfigurationCommandOutput>;
  updateFunctionConfiguration(
    args: UpdateFunctionConfigurationCommandInput,
    cb: (err: any, data?: UpdateFunctionConfigurationCommandOutput) => void
  ): void;
  updateFunctionConfiguration(
    args: UpdateFunctionConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateFunctionConfigurationCommandOutput) => void
  ): void;
  updateFunctionEventInvokeConfig(
    args: UpdateFunctionEventInvokeConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateFunctionEventInvokeConfigCommandOutput>;
  updateFunctionEventInvokeConfig(
    args: UpdateFunctionEventInvokeConfigCommandInput,
    cb: (err: any, data?: UpdateFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  updateFunctionEventInvokeConfig(
    args: UpdateFunctionEventInvokeConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateFunctionEventInvokeConfigCommandOutput) => void
  ): void;
  updateFunctionUrlConfig(
    args: UpdateFunctionUrlConfigCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UpdateFunctionUrlConfigCommandOutput>;
  updateFunctionUrlConfig(
    args: UpdateFunctionUrlConfigCommandInput,
    cb: (err: any, data?: UpdateFunctionUrlConfigCommandOutput) => void
  ): void;
  updateFunctionUrlConfig(
    args: UpdateFunctionUrlConfigCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UpdateFunctionUrlConfigCommandOutput) => void
  ): void;
}
export declare class Lambda extends LambdaClient implements Lambda {}
