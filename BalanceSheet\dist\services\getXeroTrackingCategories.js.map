{"version": 3, "file": "getXeroTrackingCategories.js", "sourceRoot": "", "sources": ["../../src/services/getXeroTrackingCategories.ts"], "names": [], "mappings": ";;;;;AAqBA,8DA8CC;AAnED,2EAA2C;AAC3C,uDAAsD;AAoB/C,KAAK,UAAU,yBAAyB,CAC3C,WAAmB,EACnB,QAAgB;IAEhB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,2BAAa,GAAE,CAAC;IACpC,MAAM,GAAG,GAAG,GAAG,OAAO,oBAAoB,CAAC;IAE3C,IAAI,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,uBAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAClC,OAAO,EAAE;gBACL,aAAa,EAAE,UAAU,WAAW,EAAE;gBACtC,gBAAgB,EAAE,QAAQ;gBAC1B,MAAM,EAAE,kBAAkB;aAC7B;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;YAC5D,UAAU,EAAE,QAAQ,CAAC,kBAAkB;YACvC,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;gBAC7C,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;gBACzC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;aACxB,CAAC,CAAC,IAAI,EAAE;SACZ,CAAC,CAAC,CAAC;IACR,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAGlE,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CACX,gFAAgF,UAAU,WAAW,CACxG,CAAC;QACN,CAAC;QAGD,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QACxE,OAAO,EAAE,CAAC;IACd,CAAC;AACL,CAAC"}