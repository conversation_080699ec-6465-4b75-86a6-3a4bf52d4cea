"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testS3JsonStorage = testS3JsonStorage;
exports.createSampleTrackingData = createSampleTrackingData;
exports.createSampleSummaryData = createSampleSummaryData;
const s3JsonStorage_1 = require("../utils/s3JsonStorage");
function createSampleTrackingData() {
    return [
        {
            CompanyId: 'test-company-123',
            Year: 2024,
            Month: 1,
            AccountId: 'ACC-001',
            AccountName: 'Cash at Bank',
            Amount: 15000.50,
            TrackingCategoryId1: 'TRACK-001',
            TrackingCategoryId2: 'TRACK-002',
        },
        {
            CompanyId: 'test-company-123',
            Year: 2024,
            Month: 1,
            AccountId: 'ACC-002',
            AccountName: 'Accounts Receivable',
            Amount: 8500.75,
            TrackingCategoryId1: 'TRACK-001',
            TrackingCategoryId2: null,
        },
        {
            CompanyId: 'test-company-123',
            Year: 2024,
            Month: 1,
            AccountId: 'ACC-003',
            AccountName: 'Inventory',
            Amount: 12000.00,
            TrackingCategoryId1: null,
            TrackingCategoryId2: 'TRACK-003',
        },
    ];
}
function createSampleSummaryData() {
    return [
        {
            CompanyId: 'test-company-123',
            Year: 2024,
            Month: 1,
            AccountId: 'ACC-001',
            AccountName: 'Cash at Bank',
            Amount: 15000.50,
        },
        {
            CompanyId: 'test-company-123',
            Year: 2024,
            Month: 1,
            AccountId: 'ACC-002',
            AccountName: 'Accounts Receivable',
            Amount: 8500.75,
        },
        {
            CompanyId: 'test-company-123',
            Year: 2024,
            Month: 1,
            AccountId: 'ACC-003',
            AccountName: 'Inventory',
            Amount: 12000.00,
        },
    ];
}
async function testS3JsonStorage() {
    console.log('🧪 Starting S3 JSON Storage Test...\n');
    try {
        const companyId = 'test-company-123';
        const year = 2024;
        const month = 1;
        console.log(`📊 Test Parameters:`);
        console.log(`   Company ID: ${companyId}`);
        console.log(`   Year: ${year}`);
        console.log(`   Month: ${month}\n`);
        const trackingData = createSampleTrackingData();
        const summaryData = createSampleSummaryData();
        console.log(`📝 Sample Data Created:`);
        console.log(`   Tracking records: ${trackingData.length}`);
        console.log(`   Summary records: ${summaryData.length}\n`);
        console.log('🚀 Testing JSON storage and S3 upload...\n');
        await (0, s3JsonStorage_1.storeMonthlyBalanceSheetJson)(companyId, year, month, trackingData, summaryData);
        console.log('\n✅ S3 JSON Storage Test Completed Successfully!');
        console.log('\n📋 What was tested:');
        console.log('   ✓ JSON file creation with proper structure');
        console.log('   ✓ Local file storage in /tmp directory');
        console.log('   ✓ S3 upload (if configured and not in offline mode)');
        console.log('   ✓ File cleanup after successful upload');
        console.log('   ✓ Error handling and logging');
    }
    catch (error) {
        console.error('\n❌ S3 JSON Storage Test Failed:');
        console.error(error);
        if (error instanceof Error) {
            console.error('\n🔍 Error Details:');
            console.error(`   Message: ${error.message}`);
            console.error(`   Stack: ${error.stack}`);
        }
        console.log('\n💡 Troubleshooting Tips:');
        console.log('   1. Check your environment variables (.env file)');
        console.log('   2. Ensure AWS credentials are configured');
        console.log('   3. Verify S3 bucket exists and is accessible');
        console.log('   4. Check network connectivity');
        process.exit(1);
    }
}
function displayEnvironmentInfo() {
    console.log('🌍 Environment Information:');
    console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
    console.log(`   IS_OFFLINE: ${process.env.IS_OFFLINE || 'not set'}`);
    console.log(`   REGION: ${process.env.REGION || 'not set'}`);
    console.log(`   S3_BUCKET_NAME: ${process.env.S3_BUCKET_NAME || 'not set'}`);
    console.log(`   ACCESS_KEY_ID: ${process.env.ACCESS_KEY_ID ? '***configured***' : 'not set'}`);
    console.log(`   SECRET_ACCESS_KEY: ${process.env.SECRET_ACCESS_KEY ? '***configured***' : 'not set'}\n`);
}
if (require.main === module) {
    require('dotenv').config();
    displayEnvironmentInfo();
    testS3JsonStorage().catch((error) => {
        console.error('Unhandled error in test:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=s3JsonStorageTest.js.map