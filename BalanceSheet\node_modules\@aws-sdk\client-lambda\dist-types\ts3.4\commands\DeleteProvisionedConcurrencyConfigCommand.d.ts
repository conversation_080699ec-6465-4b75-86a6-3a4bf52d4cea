import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { DeleteProvisionedConcurrencyConfigRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteProvisionedConcurrencyConfigCommandInput
  extends DeleteProvisionedConcurrencyConfigRequest {}
export interface DeleteProvisionedConcurrencyConfigCommandOutput
  extends __MetadataBearer {}
declare const DeleteProvisionedConcurrencyConfigCommand_base: {
  new (
    input: DeleteProvisionedConcurrencyConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteProvisionedConcurrencyConfigCommandInput,
    DeleteProvisionedConcurrencyConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteProvisionedConcurrencyConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteProvisionedConcurrencyConfigCommandInput,
    DeleteProvisionedConcurrencyConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteProvisionedConcurrencyConfigCommand extends DeleteProvisionedConcurrencyConfigCommand_base {
  protected static __types: {
    api: {
      input: DeleteProvisionedConcurrencyConfigRequest;
      output: {};
    };
    sdk: {
      input: DeleteProvisionedConcurrencyConfigCommandInput;
      output: DeleteProvisionedConcurrencyConfigCommandOutput;
    };
  };
}
