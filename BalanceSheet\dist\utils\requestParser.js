"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseRequestData = parseRequestData;
const types_1 = require("../types");
function parseRequestData(event) {
    if ('Records' in event && event.Records?.[0]) {
        try {
            const parsed = JSON.parse(event.Records[0].body);
            return parsed;
        }
        catch {
            throw new types_1.ValidationError('Failed to parse SQS message body');
        }
    }
    if ('body' in event && event.body) {
        try {
            const parsed = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
            return parsed;
        }
        catch {
            throw new types_1.ValidationError('Failed to parse API Gateway body');
        }
    }
    throw new types_1.ValidationError('Invalid request data');
}
//# sourceMappingURL=requestParser.js.map