import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  ListFunctionEventInvokeConfigsRequest,
  ListFunctionEventInvokeConfigsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListFunctionEventInvokeConfigsCommandInput
  extends ListFunctionEventInvokeConfigsRequest {}
export interface ListFunctionEventInvokeConfigsCommandOutput
  extends ListFunctionEventInvokeConfigsResponse,
    __MetadataBearer {}
declare const ListFunctionEventInvokeConfigsCommand_base: {
  new (
    input: ListFunctionEventInvokeConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFunctionEventInvokeConfigsCommandInput,
    ListFunctionEventInvokeConfigsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListFunctionEventInvokeConfigsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFunctionEventInvokeConfigsCommandInput,
    ListFunctionEventInvokeConfigsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListFunctionEventInvokeConfigsCommand extends ListFunctionEventInvokeConfigsCommand_base {
  protected static __types: {
    api: {
      input: ListFunctionEventInvokeConfigsRequest;
      output: ListFunctionEventInvokeConfigsResponse;
    };
    sdk: {
      input: ListFunctionEventInvokeConfigsCommandInput;
      output: ListFunctionEventInvokeConfigsCommandOutput;
    };
  };
}
