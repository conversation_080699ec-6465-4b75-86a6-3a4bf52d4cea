import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  EventSourceMappingConfiguration,
  GetEventSourceMappingRequest,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetEventSourceMappingCommandInput
  extends GetEventSourceMappingRequest {}
export interface GetEventSourceMappingCommandOutput
  extends EventSourceMappingConfiguration,
    __MetadataBearer {}
declare const GetEventSourceMappingCommand_base: {
  new (
    input: GetEventSourceMappingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEventSourceMappingCommandInput,
    GetEventSourceMappingCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetEventSourceMappingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetEventSourceMappingCommandInput,
    GetEventSourceMappingCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetEventSourceMappingCommand extends GetEventSourceMappingCommand_base {
  protected static __types: {
    api: {
      input: GetEventSourceMappingRequest;
      output: EventSourceMappingConfiguration;
    };
    sdk: {
      input: GetEventSourceMappingCommandInput;
      output: GetEventSourceMappingCommandOutput;
    };
  };
}
