import { LoadedConfigSelectors } from "@smithy/node-config-provider";
export declare const S3_EXPRESS_BUCKET_TYPE = "Directory";
export declare const S3_EXPRESS_BACKEND = "S3Express";
export declare const S3_EXPRESS_AUTH_SCHEME = "sigv4-s3express";
export declare const SESSION_TOKEN_QUERY_PARAM = "X-Amz-S3session-Token";
export declare const SESSION_TOKEN_HEADER: string;
export declare const NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_ENV_NAME =
  "AWS_S3_DISABLE_EXPRESS_SESSION_AUTH";
export declare const NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_INI_NAME =
  "s3_disable_express_session_auth";
export declare const NODE_DISABLE_S3_EXPRESS_SESSION_AUTH_OPTIONS: LoadedConfigSelectors<boolean>;
