import { Buffer } from "buffer";
/**
 * @internal
 */
export declare const fromArrayBuffer: (input: <PERSON><PERSON><PERSON>Buffer, offset?: number, length?: number) => Buffer;
/**
 * @internal
 */
export type StringEncoding = "ascii" | "utf8" | "utf16le" | "ucs2" | "base64" | "latin1" | "binary" | "hex";
/**
 * @internal
 */
export declare const fromString: (input: string, encoding?: StringEncoding) => Buffer;
