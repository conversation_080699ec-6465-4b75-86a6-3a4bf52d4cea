import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { DeleteFunctionRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteFunctionCommandInput extends DeleteFunctionRequest {}
export interface DeleteFunctionCommandOutput extends __MetadataBearer {}
declare const DeleteFunctionCommand_base: {
  new (
    input: DeleteFunctionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFunctionCommandInput,
    DeleteFunctionCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteFunctionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFunctionCommandInput,
    DeleteFunctionCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteFunctionCommand extends DeleteFunctionCommand_base {
  protected static __types: {
    api: {
      input: DeleteFunctionRequest;
      output: {};
    };
    sdk: {
      input: DeleteFunctionCommandInput;
      output: DeleteFunctionCommandOutput;
    };
  };
}
