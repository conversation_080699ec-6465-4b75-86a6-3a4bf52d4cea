export interface XeroRequestData {
    userId: string;
    companyId: string;
    startDate?: string;
    endDate?: string;
    dumpToDatabase?: boolean;
}
export interface XeroTokenResponse {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
}
export interface ProcessedRowDataWithoutTracking {
    Year: number;
    Month: number;
    AccountId: string;
    AccountName: string;
    Amount: number;
    CompanyId: string;
}
export interface XeroReportHeader {
    Time: string;
    ReportName: string;
    Currency: string;
    ReportBasis?: string;
    StartPeriod?: string;
    EndPeriod?: string;
    SummarizeColumnsBy?: string;
    Option?: Array<{
        Name: string;
        Value: string;
    }>;
}
export interface XeroColumnMetaData {
    Name: string;
    Value: string;
}
export interface XeroColumn {
    ColTitle: string;
    ColType: string;
    MetaData?: XeroColumnMetaData[];
}
export interface XeroColumns {
    Column: XeroColumn[];
}
export interface XeroRowData {
    value: string;
    id?: string;
}
export interface XeroRow {
    ColData: XeroRowData[];
    group?: string;
}
export interface XeroRows {
    Row: XeroRow[];
}
export interface XeroBalanceSheetReport {
    Header: XeroReportHeader;
    Columns: XeroColumns;
    Rows: XeroRows;
}
export interface XeroApiResponse {
    ProfitAndLoss?: any;
    BalanceSheet?: XeroBalanceSheetReport;
    Header?: XeroReportHeader;
    Columns?: XeroColumns;
    Rows?: XeroRows;
}
export interface ProcessedRowData {
    Year: number;
    Month: number;
    AccountId: string;
    AccountName: string;
    Amount: number;
    TrackingCategoryId1?: string | null;
    TrackingCategoryId2?: string | null;
    CompanyId: string;
}
export interface EnvironmentConfig {
    DATABASE_URL: string;
    XERO_CLIENT_ID: string;
    XERO_CLIENT_SECRET: string;
    XERO_TOKEN_URL?: string;
    XERO_BASE_URL?: string;
    FIRST_RETRY?: string;
    SECOND_RETRY?: string;
    LAST_RETRY?: string;
    REGION?: string;
    ACCESS_KEY_ID?: string;
    SECRET_ACCESS_KEY?: string;
    S3_BUCKET_NAME?: string;
    IS_OFFLINE?: string;
    PRISMA_QUERY_ENGINE_LIBRARY?: string;
    AWS_LAMBDA_FUNCTION_NAME?: string;
}
export declare class XeroError extends Error {
    statusCode?: number | undefined;
    originalError?: any | undefined;
    constructor(message: string, statusCode?: number | undefined, originalError?: any | undefined);
}
export declare class TokenRefreshError extends XeroError {
    constructor(message: string, originalError?: any);
}
export declare class ValidationError extends Error {
    missingFields?: string[] | undefined;
    constructor(message: string, missingFields?: string[] | undefined);
}
export type NonNullable<T> = T extends null | undefined ? never : T;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
//# sourceMappingURL=index.d.ts.map